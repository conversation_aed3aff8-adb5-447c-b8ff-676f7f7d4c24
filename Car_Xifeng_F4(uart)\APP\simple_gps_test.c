/**
 * @file simple_gps_test.c
 * @brief 简单的GPS测试程序
 * 
 * 这个文件展示了如何使用简化的GPS功能
 */

#include "GPS_app.h"

/**
 * @brief 测试GPS解析功能
 */
void GPS_Test_ParseExample(void)
{
    // 测试GPGGA语句解析
    char test_sentence[] = "$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47";
    
    my_printf(&huart1, "Testing GPS parsing with example data...\r\n");
    my_printf(&huart1, "Test sentence: %s\r\n", test_sentence);
    
    if (GPS_ParseNMEA(test_sentence)) {
        my_printf(&huart1, "✓ GPS parsing successful!\r\n");
        GPS_PrintData();
    } else {
        my_printf(&huart1, "✗ GPS parsing failed\r\n");
    }
}

/**
 * @brief 显示GPS使用说明
 */
void GPS_Test_ShowInstructions(void)
{
    my_printf(&huart1, "\r\n=== GPS ATGM336H Test Instructions ===\r\n");
    my_printf(&huart1, "1. Connect ATGM336H GPS module:\r\n");
    my_printf(&huart1, "   VCC -> 3.3V\r\n");
    my_printf(&huart1, "   GND -> GND\r\n");
    my_printf(&huart1, "   TX  -> PC11 (USART3_RX)\r\n");
    my_printf(&huart1, "\r\n");
    my_printf(&huart1, "2. Place GPS antenna outdoors or near window\r\n");
    my_printf(&huart1, "3. Wait for GPS fix (may take 30s-3min for cold start)\r\n");
    my_printf(&huart1, "4. GPS data will be displayed automatically every 10s\r\n");
    my_printf(&huart1, "\r\n");
    my_printf(&huart1, "Expected output when GPS gets fix:\r\n");
    my_printf(&huart1, "GPS: Lat=48.117300, Lon=11.516667, Alt=545.4m, Sats=8\r\n");
    my_printf(&huart1, "=====================================\r\n\r\n");
}

/**
 * @brief 检查GPS状态
 */
void GPS_Test_CheckStatus(void)
{
    my_printf(&huart1, "=== GPS Status Check ===\r\n");
    
    if (GPS_IsDataValid()) {
        GPS_Data_t* gps = GPS_GetData();
        my_printf(&huart1, "✓ GPS has valid fix\r\n");
        my_printf(&huart1, "Position: %.6f°, %.6f°\r\n", gps->latitude, gps->longitude);
        my_printf(&huart1, "Satellites: %d\r\n", gps->satellites);
        my_printf(&huart1, "Updates: %lu\r\n", gps->update_count);
    } else {
        my_printf(&huart1, "⏳ GPS searching for satellites...\r\n");
        my_printf(&huart1, "Make sure GPS antenna is outdoors\r\n");
    }
    
    my_printf(&huart1, "========================\r\n");
}

/* 
 * 使用示例：
 * 
 * 在主程序中调用：
 * 
 * int main(void) {
 *     // ... 系统初始化 ...
 *     
 *     // 初始化GPS
 *     Uart3_Init();
 *     
 *     // 显示使用说明
 *     GPS_Test_ShowInstructions();
 *     
 *     // 测试解析功能
 *     GPS_Test_ParseExample();
 *     
 *     while (1) {
 *         // 运行GPS任务
 *         Uart3_Task();
 *         
 *         // 每30秒检查一次状态
 *         static uint32_t last_check = 0;
 *         if (HAL_GetTick() - last_check > 30000) {
 *             GPS_Test_CheckStatus();
 *             last_check = HAL_GetTick();
 *         }
 *         
 *         HAL_Delay(100);
 *     }
 * }
 */
