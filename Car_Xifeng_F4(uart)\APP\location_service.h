#ifndef __LOCATION_SERVICE_H__
#define __LOCATION_SERVICE_H__

#include "main.h"
#include "usart.h"
#include <stdint.h>
#include <string.h>
#include <stdio.h>

/* 定位服务配置 */
#define LOCATION_UPDATE_INTERVAL_MS     5000    // 定位更新间隔 (5秒)
#define EMERGENCY_UPDATE_INTERVAL_MS    1000    // 紧急情况更新间隔 (1秒)
#define MAX_LOCATION_HISTORY           10       // 最大位置历史记录数

/* GPS坐标结构体 */
typedef struct {
    double latitude;        // 纬度 (度)
    double longitude;       // 经度 (度)
    float altitude;         // 海拔 (米)
    float accuracy;         // 精度 (米)
    uint32_t timestamp;     // 时间戳
    uint8_t valid;          // 数据有效标志
} GPS_Coordinate_t;

/* 华为云设备信息 */
typedef struct {
    char device_id[64];     // 设备ID
    char product_id[64];    // 产品ID
    char secret[128];       // 设备密钥
    uint8_t connected;      // 连接状态
} HuaweiCloud_Config_t;

/* 紧急事件类型 */
typedef enum {
    EMERGENCY_NONE = 0,
    EMERGENCY_FALL_DETECTED,
    EMERGENCY_FALL_CONFIRMED,
    EMERGENCY_SOS_BUTTON,
    EMERGENCY_LOW_BATTERY,
    EMERGENCY_DEVICE_OFFLINE
} Emergency_Type_t;

/* 紧急事件结构体 */
typedef struct {
    Emergency_Type_t type;
    GPS_Coordinate_t location;
    uint32_t timestamp;
    char description[128];
    uint8_t sent_to_cloud;
    uint8_t acknowledged;
} Emergency_Event_t;

/* 定位服务结构体 */
typedef struct {
    GPS_Coordinate_t current_location;
    GPS_Coordinate_t location_history[MAX_LOCATION_HISTORY];
    uint8_t history_index;
    uint8_t history_count;
    
    HuaweiCloud_Config_t cloud_config;
    Emergency_Event_t current_emergency;
    
    uint32_t last_update_time;
    uint32_t last_cloud_sync_time;
    uint8_t emergency_mode;
    
    // 统计信息
    uint32_t total_updates;
    uint32_t cloud_sync_count;
    uint32_t emergency_count;
} LocationService_t;

/* 全局定位服务实例 */
extern LocationService_t location_service;

/* 函数声明 */

/* 初始化和配置 */
uint8_t LocationService_Init(void);
uint8_t LocationService_SetCloudConfig(const char* device_id, const char* product_id, const char* secret);

/* 定位数据管理 */
uint8_t LocationService_UpdateLocation(double lat, double lon, float alt, float accuracy);
GPS_Coordinate_t* LocationService_GetCurrentLocation(void);
uint8_t LocationService_IsLocationValid(void);

/* 华为云集成 */
uint8_t LocationService_ConnectToCloud(void);
uint8_t LocationService_SyncToCloud(void);
uint8_t LocationService_SendEmergencyAlert(Emergency_Type_t type, const char* description);

/* 紧急事件处理 */
uint8_t LocationService_TriggerEmergency(Emergency_Type_t type, const char* description);
uint8_t LocationService_ClearEmergency(void);
uint8_t LocationService_IsEmergencyActive(void);

/* OpenStreetMap集成 */
void LocationService_GenerateMapURL(char* url_buffer, size_t buffer_size);
void LocationService_GenerateEmergencyMapURL(char* url_buffer, size_t buffer_size);

/* 任务和状态 */
void LocationService_Task(void);
void LocationService_PrintStatus(void);
void LocationService_PrintLocationHistory(void);

/* 工具函数 */
float LocationService_CalculateDistance(GPS_Coordinate_t* pos1, GPS_Coordinate_t* pos2);
uint8_t LocationService_IsMoving(float threshold_meters);

#endif
