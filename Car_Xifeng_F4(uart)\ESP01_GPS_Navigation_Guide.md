# ESP01 GPS导航系统使用指南

## 🚀 系统概述

这是一个基于ESP01 WiFi模块的车载GPS导航系统，具备以下核心功能：

### 主要功能
1. **WiFi连接管理** - 自动连接到指定WiFi网络
2. **实时GPS定位** - 上传GPS坐标到ThingSpeak云服务
3. **在线地图显示** - 在网页地图上实时显示车辆位置
4. **串口命令控制** - 通过串口1发送导航命令
5. **路径规划** - 计算并显示导航路线

## 📡 网络配置

### WiFi设置
- **网络名称**: Tenda_ZC_5G
- **密码**: zhongchuang
- **连接方式**: 自动连接，支持重试机制

### 云服务配置
- **服务商**: ThingSpeak
- **API密钥**: LU22ZUP4ZTFK4IY9
- **频道号**: 3014831
- **服务器**: api.thingspeak.com

## 🗺️ 地图服务

### 在线地图访问
- **地图网址**: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/
- **功能**: 实时显示车辆位置和导航路线
- **更新频率**: 每15秒自动更新

### 数据字段说明
- **field1**: 当前纬度
- **field2**: 当前经度  
- **field3**: 海拔高度
- **field4**: 目标纬度（导航模式）
- **field5**: 导航标识（1=导航中，0=普通定位）

## 🎮 串口命令控制

### 基础命令
```
hello           # 测试系统响应
esp_status      # 查看ESP01状态
gps_status      # 查看GPS状态
get_location    # 获取当前位置
```

### 导航命令
```
wangda          # 导航到王达广场
gaotie          # 导航到高铁站
nav_help        # 显示导航帮助
nav_list        # 显示可用目的地
nav_status      # 显示导航状态
nav_stop        # 停止导航
```

## 🔧 系统工作流程

### 1. 系统启动
```
🚀 ========== ESP01 WiFi系统启动 ==========
📡 WiFi网络: Tenda_ZC_5G
🌐 云服务: ThingSpeak (频道 3014831)
🗺️ 地图服务: 实时GPS定位与导航
==========================================
```

### 2. WiFi连接过程
```
📡 Step 1: 测试ESP01通信...
🔄 Step 2: 重启ESP01模块...
⚙️ Step 3: 设置WiFi模式...
🔗 Step 4: 连接WiFi网络 [Tenda_ZC_5G]...
✅ ESP01: WiFi连接成功！
```

### 3. GPS数据上传
```
📍 开始GPS数据上传...
📍 位置: 26.886930°N, 112.675812°E, 50.0m
🌐 URL: api.thingspeak.com/update?api_key=...
✅ GPS数据上传请求已发送！
```

### 4. 导航数据上传
```
🧭 ========== 导航数据上传 ==========
📍 当前位置: 26.886930°N, 112.675812°E
🎯 目标位置: 26.8869°N, 112.6758°E
📏 距离: 1250米
🧭 方向: 45度
✅ 导航数据上传成功！
```

## 🛠️ 技术特性

### 连接管理
- **持久连接**: 使用keep-alive保持TCP连接
- **自动重连**: WiFi断线自动重连机制
- **错误处理**: 完善的超时和重试机制

### 数据传输
- **上传频率**: 每15秒自动上传GPS数据
- **数据格式**: HTTP GET请求到ThingSpeak API
- **连接复用**: 避免频繁建立/断开TCP连接

### GPS处理
- **真实GPS**: 优先使用GPS模块数据
- **备用位置**: GPS无效时使用衡阳师范学院坐标
- **精度标识**: 区分真实GPS和备用位置

## 📍 预设目的地

### 可用目的地
1. **王达广场** (wangda)
   - 坐标: 26.8869°N, 112.6758°E
   - 描述: 酃湖万达广场

2. **高铁站** (gaotie)  
   - 坐标: 26.8945°N, 112.6712°E
   - 描述: 衡阳东站

### 添加新目的地
在`navigation_app.c`中的`destinations`数组添加：
```c
{"目的地代码", 纬度, 经度, "目的地描述"}
```

## 🔍 故障排除

### 常见问题
1. **WiFi连接失败**
   - 检查网络名称和密码
   - 确认信号强度
   - 查看重试次数

2. **GPS数据无效**
   - 将设备移到室外
   - 等待GPS冷启动
   - 检查GPS模块连接

3. **地图不显示**
   - 检查网络连接
   - 刷新网页
   - 确认ThingSpeak数据上传

### 调试信息
系统提供详细的调试输出，包括：
- WiFi连接状态
- TCP连接状态  
- GPS数据状态
- HTTP请求详情

## 🎯 使用示例

### 基本定位
1. 系统启动后自动连接WiFi
2. 每15秒上传GPS位置到云端
3. 访问地图网址查看实时位置

### 导航使用
1. 发送命令: `wangda`
2. 系统计算路线并上传导航数据
3. 地图显示当前位置到目标的路线
4. 实时更新导航进度

---

**开发者**: AI助手  
**版本**: 2.0  
**更新日期**: 2025-01-25
