# ESP01 TCP连接问题调试步骤

## 当前问题分析

从您的日志看到：
- ✅ WiFi连接成功 (`WIFI GOT IP`)
- ❌ TCP连接在20秒后超时失败
- ❌ 可能是DNS解析或网络连接问题

## 立即测试步骤

### 1. 基础网络诊断
```
esp_diag
```
这会检查：
- WiFi连接状态
- IP地址获取
- 连接状态
- DNS解析测试

### 2. 分步测试连接

**步骤A：检查ESP01状态**
```
esp_status
```

**步骤B：测试IP直连（绕过DNS）**
```
esp_ip_test
```
这会直接使用ThingSpeak的IP地址 `***************` 进行连接测试

**步骤C：如果IP连接成功，测试完整连接**
```
esp_tcp_test
```

### 3. 如果仍然失败，重置并重试

**完全重置：**
```
esp_force_reset
```
等待5秒后：
```
esp_start
```

## 可能的问题原因

### 1. DNS解析问题
- **症状**：域名连接失败，但IP连接成功
- **解决**：使用IP地址作为备用方案

### 2. 网络防火墙/路由器限制
- **症状**：所有连接都失败
- **检查**：路由器是否阻止ESP01访问外网

### 3. ThingSpeak服务器问题
- **症状**：连接超时
- **检查**：ThingSpeak服务是否正常

### 4. ESP01模块硬件问题
- **症状**：WiFi连接不稳定
- **检查**：电源、天线、温度

## 新增的改进功能

### 1. 双重连接策略
- 首先尝试域名连接（10秒超时）
- 如果失败，自动切换到IP地址连接
- 提高连接成功率

### 2. 更详细的错误诊断
- DNS解析状态监控
- 连接失败原因分析
- 分阶段进度显示

### 3. 智能重试机制
- 自动检测连接类型
- 根据错误类型选择重试策略
- 避免无效重试

## 预期测试结果

### 如果 `esp_ip_test` 成功：
- 说明网络连接正常，DNS解析有问题
- 系统会自动使用IP地址作为备用方案
- GPS数据上传应该能正常工作

### 如果 `esp_ip_test` 也失败：
- 说明网络连接本身有问题
- 需要检查WiFi网络、路由器设置
- 可能需要更换网络环境测试

## 调试命令总结

```bash
# 基础诊断
esp_status          # 查看当前状态
esp_diag           # 完整网络诊断

# 连接测试
esp_ip_test        # IP地址直连测试
esp_tcp_test       # 完整TCP连接测试

# 重置命令
esp_reset          # 软重置
esp_force_reset    # 强制重置

# 数据传输
send_location      # 发送GPS数据
get_location       # 获取当前位置
```

## 下一步行动

1. **立即执行**：`esp_diag` 查看详细状态
2. **关键测试**：`esp_ip_test` 验证基础连接
3. **根据结果**：选择相应的解决方案

请按顺序执行这些命令，并告诉我每个命令的输出结果，这样我可以准确定位问题所在。
