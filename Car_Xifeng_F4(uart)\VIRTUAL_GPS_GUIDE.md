# 🎯 虚拟GPS系统使用指南 - 衡阳师范学院

## 📍 系统概述

本系统为STM32F407单片机配置了虚拟GPS数据生成器，模拟衡阳师范学院的GPS定位数据，并通过ESP-01模块实时上传到ThingSpeak云平台。

## 🏫 定位信息

- **基准位置**: 衡阳师范学院
- **纬度**: 26.900717°N  
- **经度**: 112.573808°E
- **海拔**: 50米
- **移动模拟**: 在100米半径内随机移动

## 🔧 系统配置

### 硬件连接
```
ESP-01 -> STM32F407
VCC    -> 3.3V
GND    -> GND  
TX     -> PA3 (USART2_RX)
RX     -> PA2 (USART2_TX)
```

### ThingSpeak配置
- **Channel ID**: 3014831
- **Write API Key**: LU22ZUP4ZTFK4IY9
- **Field1**: 纬度 (Latitude)
- **Field2**: 经度 (Longitude)  
- **Field3**: 海拔 (Altitude)

## 🚀 使用方法

### 1. 编译和烧录
```bash
# 使用Keil MDK编译项目
# 烧录到STM32F407开发板
```

### 2. 系统启动
系统启动后会自动：
1. 初始化虚拟GPS数据生成器
2. 设置衡阳师范学院为基准位置
3. 启动ESP-01 WiFi连接
4. 开始定期上传GPS数据

### 3. 串口监控
连接串口1 (115200波特率) 查看系统状态：
```
Virtual GPS initialized for Hengyang Normal University
Base Location: 26.900717°N, 112.573808°E, 50.0m
ESP-01: Connecting to WiFi...
ESP-01: Connected successfully
Virtual GPS: 26.900717°N, 112.573808°E, 50.0m [Update #10]
GPS Data uploaded: Hengyang Normal University 26.900717°N,112.573808°E,50.0m
```

## 📊 数据上传

### 上传频率
- **GPS数据生成**: 每5秒生成新的虚拟GPS数据
- **ThingSpeak上传**: 每15秒上传一次到云平台
- **移动模拟**: 圆形轨迹，半径约100米

### 数据格式
```http
GET /update?api_key=LU22ZUP4ZTFK4IY9&field1=26.900717&field2=112.573808&field3=50.0 HTTP/1.1
Host: api.thingspeak.com
Connection: close
```

## 🌐 网页监控

访问部署的网页查看实时GPS追踪：
- 地图会显示衡阳师范学院位置
- 实时更新GPS坐标
- 显示移动轨迹

## ⚙️ 高级配置

### 修改基准位置
```c
// 在代码中调用
GPS_Virtual_SetLocation(26.900717f, 112.573808f, 50.0f);
```

### 启用/禁用移动模拟
```c
GPS_Virtual_EnableMovement(1);  // 启用移动
GPS_Virtual_EnableMovement(0);  // 禁用移动，固定位置
```

### 调整移动范围
修改 `GPS_app.c` 中的 `movement_radius` 参数：
```c
virtual_gps.movement_radius = 0.001f;  // 约100米半径
virtual_gps.movement_radius = 0.002f;  // 约200米半径
```

## 🔍 故障排除

### 常见问题

1. **ESP-01连接失败**
   - 检查WiFi密码和网络名称
   - 确认ESP-01供电稳定
   - 查看串口输出的错误信息

2. **数据未上传到ThingSpeak**
   - 验证API密钥是否正确
   - 检查网络连接
   - 确认ThingSpeak频道设置

3. **GPS数据不更新**
   - 检查虚拟GPS初始化是否成功
   - 确认系统时钟正常运行
   - 查看串口输出的调试信息

### 调试命令
通过串口1发送以下命令进行调试：
- `gps` - 显示当前GPS数据
- `status` - 显示系统状态
- `location` - 显示位置信息

## 📈 性能监控

### 系统指标
- **内存使用**: 约2KB RAM用于GPS数据缓冲
- **CPU占用**: 低于5% (主要在数据生成时)
- **功耗**: 约150mA (包含ESP-01)

### 数据精度
- **位置精度**: ±1米 (虚拟数据)
- **时间同步**: 基于系统时钟
- **更新延迟**: <1秒

## 🎯 应用场景

1. **室内GPS测试**: 无需真实GPS信号
2. **系统集成测试**: 验证数据传输链路
3. **演示展示**: 稳定的定位数据展示
4. **开发调试**: 可控的GPS数据环境

## 📝 注意事项

- 虚拟GPS数据仅用于测试和演示
- 实际部署时需要连接真实GPS模块
- 确保ThingSpeak账户有足够的数据配额
- 定期检查WiFi连接状态

## 🔄 版本更新

### v1.0 (当前版本)
- ✅ 基础虚拟GPS数据生成
- ✅ 衡阳师范学院位置配置
- ✅ ThingSpeak数据上传
- ✅ 圆形移动轨迹模拟
- ✅ 实时网页显示

### 计划功能
- 🔄 多种移动模式 (直线、随机等)
- 🔄 GPS数据记录和回放
- 🔄 移动速度控制
- 🔄 多点路径规划

---

**🎓 衡阳师范学院GPS实时追踪系统 - 让定位更智能！**
