#ifndef  __ESP01_H
#define  __ESP01_H

#include "MyDefine.h"

// GPS上传间隔定义
#define GPS_UPLOAD_INTERVAL 10000  // 10秒间隔

// ESP-01状态枚举
typedef enum {
    ESP01_STATE_IDLE = 0,
    ESP01_STATE_INIT,
    ESP01_STATE_CONNECTING,
    ESP01_STATE_CONNECTED,
    ESP01_STATE_ERROR
} ESP01_State_t;

// 函数声明
void esp01_Init(void);
void esp01_Task(void);
void esp01_InitSequence(void);
void esp01_CheckConnection(void);
ESP01_State_t esp01_GetState(void);
void esp01_SetConnected(void);
void esp01_SetTCPConnected(void);
void esp01_SetDataSendReady(void);
void esp01_ResetTCPState(void);
void esp01_Reset(void);
void esp01_SendCommand(const char* command);
void esp01_StartInit(void);
void esp01_NetworkDiagnostics(void);
void esp01_ForceReset(void);

// TCP连接相关函数
uint8_t esp01_EstablishTCPConnection(void);
uint8_t esp01_TryTCPWithIP(void);
uint8_t esp01_CheckTCPStatus(void);
uint8_t esp01_CheckAndRecoverTCPConnection(void);
uint8_t esp01_SendDataWithRecovery(const char* data, uint16_t length);

// GPS定位相关函数
void esp01_GetRealLocation(float* lat, float* lon, float* alt);
void esp01_UploadGPSData(void);
void esp01_SendLocationData(void);  // 兼容性函数，调用esp01_UploadGPSData

#endif

