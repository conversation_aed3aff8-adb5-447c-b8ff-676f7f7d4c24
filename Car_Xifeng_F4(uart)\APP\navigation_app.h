#ifndef __NAVIGATION_APP_H
#define __NAVIGATION_APP_H

#include "main.h"
#include "GPS_app.h"
#include "esp01_app.h"
#include "uart_driver.h"
#include <string.h>
#include <math.h>

#define MAX_DESTINATIONS 20
#define MAX_NAME_LENGTH 32
#define MAX_WAYPOINTS 50
#define EARTH_RADIUS 6371000.0f

typedef struct {
    char name[MAX_NAME_LENGTH];
    float latitude;
    float longitude;
    char description[64];
} Destination_t;

typedef struct {
    float latitude;
    float longitude;
    float distance_to_next;
    float bearing_to_next;
    char instruction[128];
} Waypoint_t;

typedef struct {
    Destination_t destination;
    Waypoint_t waypoints[MAX_WAYPOINTS];
    uint8_t waypoint_count;
    uint8_t current_waypoint;
    float total_distance;
    float remaining_distance;
    uint8_t is_active;
    uint8_t is_arrived;
} Navigation_t;

typedef enum {
    NAV_STATE_IDLE = 0,
    NAV_STATE_PLANNING,
    NAV_STATE_NAVIGATING,
    NAV_STATE_ARRIVED,
    NAV_STATE_ERROR
} NavigationState_t;

extern Navigation_t current_navigation;
extern NavigationState_t nav_state;

void Navigation_Init(void);
void Navigation_Task(void);
uint8_t Navigation_StartNavigation(const char* destination_name);
void Navigation_StopNavigation(void);
void Navigation_ProcessCommand(const char* command);
uint8_t Navigation_FindDestination(const char* name, Destination_t* dest);
void Navigation_PlanRoute(float start_lat, float start_lon, float end_lat, float end_lon);
float Navigation_CalculateDistance(float lat1, float lon1, float lat2, float lon2);
float Navigation_CalculateBearing(float lat1, float lon1, float lat2, float lon2);
void Navigation_UpdateProgress(void);
void Navigation_PrintStatus(void);
void Navigation_PrintDestinations(void);

#endif
