/**
 * @file gps_integration_example.c
 * @brief GPS集成示例 - 展示如何在主程序中使用ATGM336H GPS模块
 * 
 * 使用说明：
 * 1. 将ATGM336H的TX连接到STM32的USART3_RX (PC11)
 * 2. 将ATGM336H的RX连接到STM32的USART3_TX (PC10) (可选，用于发送配置命令)
 * 3. 将ATGM336H的VCC连接到3.3V或5V
 * 4. 将ATGM336H的GND连接到GND
 * 5. 在室外或窗边测试，确保能接收到GPS信号
 */

#include "GPS_app.h"
#include "location_service.h"

/* 示例任务状态 */
static uint32_t last_gps_print_time = 0;
static uint32_t last_location_sync_time = 0;
static uint8_t gps_integration_initialized = 0;

/**
 * @brief GPS集成初始化
 * 在主程序的初始化部分调用
 */
void GPS_Integration_Init(void)
{
    // my_printf(&huart1, "=== GPS Integration Example ===\r\n");  // 已注释掉GPS集成示例标题
    
    // 初始化GPS模块
    GPS_Init();
    
    // 初始化定位服务 (如果需要华为云集成)
    LocationService_Init();
    
    // 设置华为云配置 (替换为你的实际配置)
    LocationService_SetCloudConfig("your_device_id", "your_product_id", "your_secret");
    
    gps_integration_initialized = 1;
    
    // my_printf(&huart1, "GPS Integration initialized\r\n");  // 已注释掉GPS集成初始化信息
    // my_printf(&huart1, "Commands: 'gps', 'status', 'location', 'sync'\r\n");  // 已注释掉命令提示
    // my_printf(&huart1, "===============================\r\n");  // 已注释掉分隔线
}

/**
 * @brief GPS集成主任务
 * 在主循环中周期性调用 (建议10Hz)
 */
void GPS_Integration_Task(void)
{
    if (!gps_integration_initialized) {
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 运行GPS任务
    GPS_Task();
    
    // 运行定位服务任务
    LocationService_Task();
    
    // 定期打印GPS状态 (每10秒)
    if (current_time - last_gps_print_time > 10000) {
        GPS_PrintStatus();
        last_gps_print_time = current_time;
    }
    
    // 如果GPS数据有效，更新定位服务
    if (GPS_IsDataValid()) {
        GPS_Data_t* gps_data = GPS_GetData();
        
        // 更新定位服务中的位置信息
        LocationService_UpdateLocation(gps_data->latitude, gps_data->longitude, 
                                     gps_data->altitude, gps_data->hdop * 5.0f);
        
        // 定期同步到华为云 (每30秒)
        if (current_time - last_location_sync_time > 30000) {
            LocationService_SyncToCloud();
            last_location_sync_time = current_time;
        }
    }
}

/**
 * @brief 处理GPS相关的串口命令
 * 在串口接收处理中调用
 */
void GPS_Integration_ProcessCommand(char* command)
{
    if (!gps_integration_initialized) {
        return;
    }
    
    if (strcmp(command, "gps") == 0) {
        // 打印GPS数据
        GPS_PrintData();
    }
    else if (strcmp(command, "status") == 0) {
        // 打印GPS状态
        GPS_PrintStatus();
    }
    else if (strcmp(command, "location") == 0) {
        // 打印定位服务状态
        LocationService_PrintStatus();
    }
    else if (strcmp(command, "sync") == 0) {
        // 手动同步到华为云
        if (GPS_IsDataValid()) {
            GPS_Data_t* gps_data = GPS_GetData();
            LocationService_UpdateLocation(gps_data->latitude, gps_data->longitude, 
                                         gps_data->altitude, gps_data->hdop * 5.0f);
            LocationService_SyncToCloud();
            my_printf(&huart1, "Manual sync completed\r\n");
        } else {
            my_printf(&huart1, "No valid GPS data to sync\r\n");
        }
    }
    else if (strcmp(command, "test") == 0) {
        // 测试紧急事件
        if (GPS_IsDataValid()) {
            LocationService_TriggerEmergency(EMERGENCY_SOS_BUTTON, "Test emergency from GPS location");
            my_printf(&huart1, "Test emergency triggered\r\n");
        } else {
            my_printf(&huart1, "No GPS fix for emergency test\r\n");
        }
    }
    else if (strcmp(command, "clear") == 0) {
        // 清除紧急状态
        LocationService_ClearEmergency();
        my_printf(&huart1, "Emergency cleared\r\n");
    }
}

/**
 * @brief GPS数据变化回调函数
 * 当GPS获得首次定位或定位丢失时调用
 */
void GPS_Integration_OnLocationChanged(uint8_t has_fix)
{
    if (has_fix) {
        GPS_Data_t* gps_data = GPS_GetData();
        my_printf(&huart1, "\r\n*** GPS FIX ACQUIRED ***\r\n");
        my_printf(&huart1, "Location: %.6f, %.6f\r\n", gps_data->latitude, gps_data->longitude);
        my_printf(&huart1, "Satellites: %d, HDOP: %.2f\r\n", gps_data->satellites, gps_data->hdop);
        
        // 生成地图链接
        my_printf(&huart1, "Google Maps: https://maps.google.com/?q=%.6f,%.6f\r\n",
                  gps_data->latitude, gps_data->longitude);
        
        // 立即同步到华为云
        LocationService_UpdateLocation(gps_data->latitude, gps_data->longitude, 
                                     gps_data->altitude, gps_data->hdop * 5.0f);
        LocationService_SyncToCloud();
        
        my_printf(&huart1, "************************\r\n\r\n");
    } else {
        my_printf(&huart1, "\r\n*** GPS FIX LOST ***\r\n");
        my_printf(&huart1, "Searching for satellites...\r\n\r\n");
    }
}

/**
 * @brief 获取当前GPS状态字符串
 */
const char* GPS_Integration_GetStatusString(void)
{
    if (!gps_integration_initialized) {
        return "NOT_INIT";
    }
    
    if (GPS_IsDataValid()) {
        GPS_Data_t* gps_data = GPS_GetData();
        static char status_str[64];
        snprintf(status_str, sizeof(status_str), "FIXED(%.6f,%.6f)", 
                gps_data->latitude, gps_data->longitude);
        return status_str;
    } else {
        return "SEARCHING";
    }
}

/**
 * @brief 检查GPS是否准备就绪
 */
uint8_t GPS_Integration_IsReady(void)
{
    return gps_integration_initialized && GPS_IsDataValid();
}

/* 
 * 在主程序中的使用示例：
 * 
 * int main(void) {
 *     // ... HAL初始化 ...
 *     
 *     // 初始化GPS集成
 *     GPS_Integration_Init();
 *     
 *     while (1) {
 *         // 运行GPS集成任务
 *         GPS_Integration_Task();
 *         
 *         // 其他任务...
 *         
 *         HAL_Delay(100); // 10Hz更新频率
 *     }
 * }
 * 
 * 在串口接收回调中：
 * void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
 *     if (huart->Instance == USART1) {
 *         // 处理命令
 *         GPS_Integration_ProcessCommand(received_command);
 *     }
 * }
 */
