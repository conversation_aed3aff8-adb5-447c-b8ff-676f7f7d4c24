#include "uart_app.h"
#include "navigation_app.h"

extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE];
extern uint8_t ring_buffer_input[BUFFER_SIZE];
extern struct rt_ringbuffer ring_buffer;
extern uint8_t uart_data_buffer[BUFFER_SIZE];

void Uart_Init(void)
{
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE);
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);

  // 只发送一次简单的启动提示
  static uint8_t init_done = 0;
  if (!init_done) {
    HAL_Delay(500);
    my_printf(&huart1, "System Ready. Type 'hello' to test.\r\n");
    init_done = 1;
  }
}


void Uart_Task(void)
{
  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);

  // 每5秒输出一次心跳信息，确认任务在运行
  static uint32_t last_heartbeat = 0;
  if (HAL_GetTick() - last_heartbeat > 5000) {
    my_printf(&huart1, "UART Task Heartbeat - Ring buffer len: %d\r\n", uart_data_len);
    last_heartbeat = HAL_GetTick();
  }

  // DMA状态检查已移除，串口输入正常工作

  if(uart_data_len > 0)
  {
    // 有数据时LED闪烁
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);
    my_printf(&huart1, "*** DATA DETECTED! Buffer len: %d ***\r\n", uart_data_len);

    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';

    // 临时启用调试输出来排查问题
    my_printf(&huart1, "RX: [%s] len=%d\r\n", uart_data_buffer, uart_data_len);

    // 清理输入数据，移除换行符
    char clean_command[64];
    strncpy(clean_command, (char*)uart_data_buffer, sizeof(clean_command) - 1);
    clean_command[sizeof(clean_command) - 1] = '\0';

    // 移除所有可能的换行符和回车符
    char* newline = strchr(clean_command, '\r');
    if (newline) *newline = '\0';
    newline = strchr(clean_command, '\n');
    if (newline) *newline = '\0';

    // 调试输出清理后的命令
    my_printf(&huart1, "Clean CMD: [%s] len=%d\r\n", clean_command, strlen(clean_command));

    // 优先处理导航命令
    if (strcmp(clean_command, "wangda") == 0)
    {
        my_printf(&huart1, "\r\n🎯 ========== 启动导航到酃湖万达 ==========\r\n");
        my_printf(&huart1, "🚗 目的地：酃湖万达广场\r\n");
        my_printf(&huart1, "📍 坐标：26.8869°N, 112.6758°E\r\n");
        my_printf(&huart1, "🛣️ 路线：遵守交通规则的最优路径\r\n");
        my_printf(&huart1, "🗺️ 正在规划路线并上传到地图...\r\n");
        my_printf(&huart1, "==========================================\r\n\r\n");

        Navigation_StartNavigation("wangda");
        return;
    }
    else if (strcmp(clean_command, "hello") == 0)
    {
        my_printf(&huart1, "System Hello Response!\r\n");
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);
    }
    else if (strcmp(clean_command, "esp_status") == 0)
    {
        ESP01_State_t state = esp01_GetState();
        switch(state)
        {
            case ESP01_STATE_IDLE:
                my_printf(&huart1, "ESP-01 Status: IDLE\r\n");
                break;
            case ESP01_STATE_INIT:
                my_printf(&huart1, "ESP-01 Status: INITIALIZING\r\n");
                break;
            case ESP01_STATE_CONNECTING:
                my_printf(&huart1, "ESP-01 Status: CONNECTING\r\n");
                break;
            case ESP01_STATE_CONNECTED:
                my_printf(&huart1, "ESP-01 Status: CONNECTED\r\n");
                break;
            case ESP01_STATE_ERROR:
                my_printf(&huart1, "ESP-01 Status: ERROR\r\n");
                break;
            default:
                my_printf(&huart1, "ESP-01 Status: UNKNOWN\r\n");
                break;
        }
    }
    else if (strcmp(clean_command, "esp_reset") == 0)
    {
        my_printf(&huart1, "Resetting ESP-01...\r\n");
        esp01_Reset();
    }
    else if (strcmp(clean_command, "esp_force_at") == 0)
    {
        Uart2_Printf(&huart2, "AT\r\n");
    }
    else if (strcmp(clean_command, "esp_test") == 0)
    {
        Uart2_Printf(&huart2, "TEST123\r\n");
    }
    else if (strcmp(clean_command, "esp_start") == 0)
    {
        esp01_StartInit();
    }
    else if (strcmp(clean_command, "esp_diag") == 0)
    {
        my_printf(&huart1, "Running ESP01 network diagnostics...\r\n");
        esp01_NetworkDiagnostics();
    }
    else if (strcmp(clean_command, "esp_force_reset") == 0)
    {
        my_printf(&huart1, "Force resetting ESP01 module...\r\n");
        esp01_ForceReset();
    }
    else if (strcmp(clean_command, "esp_tcp_test") == 0)
    {
        my_printf(&huart1, "Testing TCP connection...\r\n");
        if (esp01_EstablishTCPConnection()) {
            my_printf(&huart1, "✅ TCP connection test successful\r\n");
        } else {
            my_printf(&huart1, "❌ TCP connection test failed\r\n");
        }
    }
    else if (strcmp(clean_command, "esp_ip_test") == 0)
    {
        my_printf(&huart1, "Testing TCP connection with IP address...\r\n");
        if (esp01_TryTCPWithIP()) {
            my_printf(&huart1, "✅ IP connection test successful\r\n");
        } else {
            my_printf(&huart1, "❌ IP connection test failed\r\n");
        }
    }
    else if (strcmp(clean_command, "test_simple_http") == 0)
    {
        my_printf(&huart1, "Testing simple HTTP request to ThingSpeak...\r\n");
        // 发送简单的HTTP GET请求测试
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(5000);
        Uart2_Printf(&huart2, "AT+CIPSEND=50\r\n");
        HAL_Delay(2000);
        Uart2_Printf(&huart2, "GET /channels/3014831 HTTP/1.1\r\nHost: api.thingspeak.com\r\n\r\n");
    }
    else if (strcmp(clean_command, "test_thingspeak_api") == 0)
    {
        my_printf(&huart1, "Testing ThingSpeak API with your credentials...\r\n");
        my_printf(&huart1, "Channel: 3014831\r\n");
        my_printf(&huart1, "Write API Key: LU22ZUP4ZTFK4IY9\r\n");
        my_printf(&huart1, "Attempting direct API call...\r\n");

        // 测试ThingSpeak API
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(5000);

        char test_request[200];
        snprintf(test_request, sizeof(test_request),
                "GET /update?api_key=LU22ZUP4ZTFK4IY9&field1=26.88693&field2=112.675813&field3=50.0 HTTP/1.1\r\n"
                "Host: api.thingspeak.com\r\n\r\n");

        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", strlen(test_request));
        HAL_Delay(2000);
        Uart2_Printf(&huart2, "%s", test_request);
    }
    else if (strcmp(clean_command, "send_location") == 0)
    {
        my_printf(&huart1, "Sending location data to server...\r\n");
        esp01_SendLocationData();
    }
    else if (strcmp(clean_command, "get_location") == 0)
    {
        float lat, lon, alt;
        esp01_GetRealLocation(&lat, &lon, &alt);
        my_printf(&huart1, "Current Location: %.6f,%.6f,%.1fm\r\n", lat, lon, alt);
        my_printf(&huart1, "OpenStreetMap: https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=16\r\n", lat, lon);
    }
    else if (strcmp(clean_command, "gps_status") == 0)
    {
        my_printf(&huart1, "=== GPS Status Report ===\r\n");
        my_printf(&huart1, "Global GPS Data:\r\n");
        my_printf(&huart1, "  Latitude: %.6f\r\n", g_LatAndLongData.latitude);
        my_printf(&huart1, "  Longitude: %.6f\r\n", g_LatAndLongData.longitude);
        my_printf(&huart1, "  Direction: %c%c\r\n", g_LatAndLongData.N_S, g_LatAndLongData.E_W);
        my_printf(&huart1, "Save_Data Status:\r\n");
        my_printf(&huart1, "  isGetData: %d\r\n", Save_Data.isGetData);
        my_printf(&huart1, "  isParseData: %d\r\n", Save_Data.isParseData);
        my_printf(&huart1, "  isUsefull: %d\r\n", Save_Data.isUsefull);
        my_printf(&huart1, "  UTC Time: %s\r\n", Save_Data.UTCTime);
        my_printf(&huart1, "========================\r\n");
    }
    else
    {
        // 使用已经清理过的命令字符串
        Navigation_ProcessCommand(clean_command);
    }

    memset(uart_data_buffer, 0, uart_data_len);
  }
}


