# 🎓 衡阳师范学院GPS实时追踪测试指南

## 📍 系统概述

本系统将STM32F407单片机配置为虚拟GPS发射器，模拟衡阳师范学院的实时定位数据，并通过ESP-01上传到ThingSpeak，最终在您的网站上实时显示。

## 🏫 定位信息

- **学校**: 衡阳师范学院
- **纬度**: 26.88693°N
- **经度**: 112.675813°E
- **海拔**: 50米
- **移动模拟**: 校园内100米半径随机移动

## 🔧 测试步骤

### 1. 硬件准备
```
✅ STM32F407开发板
✅ ESP-01 WiFi模块
✅ USB转串口模块 (查看调试信息)
✅ 稳定的WiFi网络
```

### 2. 编译烧录
1. 使用Keil MDK打开项目
2. 编译代码 (确保无错误)
3. 烧录到STM32F407

### 3. 串口监控
连接串口1 (115200波特率)，应该看到：

```
Virtual GPS initialized for Hengyang Normal University
Base Location: 26.88693°N, 112.675813°E, 50.0m
ESP-01: Initializing...
ESP-01: Connecting to WiFi...
ESP-01: Connected successfully
Virtual GPS: 26.88693°N, 112.675813°E, 50.0m [Update #1]
Uploading GPS data to ThingSpeak...
URL: api.thingspeak.com/update?api_key=LU22ZUP4ZTFK4IY9&field1=26.88693&field2=112.675813&field3=50.0
✅ GPS Data uploaded successfully!
📍 Location: Hengyang Normal University 26.88693°N, 112.675813°E, 50.0m
🌐 View on web: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/
⏰ Next upload in 15 seconds...
```

### 4. 网页验证
打开您的网站：
https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/

应该看到：
- ✅ **连接状态**: 在线 (绿色)
- ✅ **当前位置**: 26.88693, 112.675813
- ✅ **地图显示**: 衡阳师范学院位置
- ✅ **实时更新**: 每15秒更新一次

## 📊 数据流程

```
STM32F407 → 虚拟GPS生成 → ESP-01 → WiFi → ThingSpeak → 您的网站
   ↓              ↓           ↓        ↓         ↓          ↓
衡阳师范学院    NMEA格式    HTTP请求   云存储    API读取    地图显示
```

## 🔍 故障排除

### 问题1: ESP-01连接失败
**现象**: 串口显示连接超时
**解决**:
```c
// 检查WiFi配置 (在esp01_app.c中)
#define WIFI_SSID "您的WiFi名称"
#define WIFI_PASSWORD "您的WiFi密码"
```

### 问题2: 数据未上传
**现象**: 串口显示上传但网站无数据
**解决**:
1. 检查ThingSpeak API密钥
2. 验证网络连接
3. 查看ThingSpeak频道状态

### 问题3: 网站显示离线
**现象**: 网站连接状态显示离线
**解决**:
1. 等待15秒让数据上传
2. 点击"刷新数据"按钮
3. 检查浏览器控制台错误

## ⚙️ 自定义配置

### 修改上传频率
```c
// 在esp01_Task()函数中修改
if(current_time - last_upload_time > 15000) // 15秒改为其他值
```

### 修改移动范围
```c
// 在GPS_Virtual_Init()函数中修改
virtual_gps.movement_radius = 0.001f;  // 0.001 ≈ 100米
```

### 禁用移动模拟
```c
// 固定在衡阳师范学院位置
GPS_Virtual_EnableMovement(0);
```

## 📈 性能指标

- **数据生成**: 每5秒
- **数据上传**: 每15秒  
- **网站更新**: 每10秒自动刷新
- **位置精度**: ±1米 (虚拟数据)
- **延迟**: 总延迟约20-30秒

## 🎯 测试验证清单

- [ ] 串口输出正常
- [ ] ESP-01连接成功
- [ ] GPS数据生成正常
- [ ] ThingSpeak上传成功
- [ ] 网站显示在线状态
- [ ] 地图定位到衡阳师范学院
- [ ] 坐标实时更新
- [ ] 移动轨迹显示

## 📱 移动端测试

在手机上打开网站链接，应该能看到：
- 响应式地图界面
- 实时GPS坐标更新
- 衡阳师范学院位置标记

## 🔄 持续运行

系统设计为24/7持续运行：
- 自动重连WiFi
- 定期上传数据
- 错误自动恢复
- 低功耗设计

## 📞 技术支持

如果遇到问题：
1. 检查串口输出日志
2. 验证网络连接
3. 确认ThingSpeak配置
4. 查看网站控制台错误

---

**🎓 现在您可以实时监控衡阳师范学院的虚拟GPS定位了！**

打开网站链接，享受实时GPS追踪体验！ 🚀
