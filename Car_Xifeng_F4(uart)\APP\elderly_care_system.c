/**
 * @file elderly_care_system.c
 * @brief 老人关爱系统 - 集成摔倒检测、定位服务和华为云
 * 
 * 功能特性：
 * 1. 实时摔倒检测
 * 2. GPS定位和华为云同步
 * 3. OpenStreetMap地图集成
 * 4. 紧急事件处理
 * 5. 远程监控和报警
 */

#include "mpu6050_app.h"
#include "location_service.h"

/* 系统配置 */
#define SYSTEM_UPDATE_INTERVAL_MS       100     // 系统更新间隔 (10Hz)
#define LOCATION_MOCK_UPDATE_MS         10000   // 模拟GPS更新间隔 (10秒)
#define STATUS_REPORT_INTERVAL_MS       30000   // 状态报告间隔 (30秒)

/* 系统状态 */
typedef struct {
    uint8_t system_initialized;
    uint8_t mpu6050_ready;
    uint8_t location_service_ready;
    uint8_t cloud_connected;
    
    uint32_t system_start_time;
    uint32_t last_status_report;
    uint32_t last_location_update;
    
    // 统计信息
    uint32_t total_runtime_seconds;
    uint32_t fall_detection_count;
    uint32_t emergency_alerts_sent;
} ElderlyCareSystem_t;

/* 全局系统实例 */
static ElderlyCareSystem_t care_system = {0};

/* 模拟GPS数据 (实际应用中应该从GPS模块获取) */
static double mock_latitude = 30.985;   // 你在华为云中看到的纬度
static double mock_longitude = 40.211;  // 你在华为云中看到的经度

/* 私有函数声明 */
static void ElderlyCareSystem_Init(void);
static void ElderlyCareSystem_UpdateLocation(void);
static void ElderlyCareSystem_HandleFallDetection(void);
static void ElderlyCareSystem_SendStatusReport(void);
static void ElderlyCareSystem_ProcessCommands(void);

/**
 * @brief 初始化老人关爱系统
 */
static void ElderlyCareSystem_Init(void)
{
    my_printf(&huart1, "=== Elderly Care System Starting ===\r\n");
    
    // 初始化MPU6050
    if (MPU6050_App_IsConnected()) {
        care_system.mpu6050_ready = 1;
        my_printf(&huart1, "MPU6050: Ready\r\n");
        
        // 如果未校准，提示用户校准
        if (!MPU6050_App_IsCalibrated()) {
            my_printf(&huart1, "WARNING: MPU6050 not calibrated!\r\n");
            my_printf(&huart1, "Please keep device still and send 'calibrate' command\r\n");
        }
    } else {
        my_printf(&huart1, "ERROR: MPU6050 not connected!\r\n");
    }
    
    // 初始化定位服务
    if (LocationService_Init() == 0) {
        care_system.location_service_ready = 1;
        my_printf(&huart1, "Location Service: Ready\r\n");
        
        // 设置华为云配置 (使用你的实际配置)
        LocationService_SetCloudConfig("your_device_id", "your_product_id", "your_secret");
        
        // 连接到华为云
        if (LocationService_ConnectToCloud() == 0) {
            care_system.cloud_connected = 1;
            my_printf(&huart1, "Huawei Cloud: Connected\r\n");
        }
    }
    
    care_system.system_start_time = HAL_GetTick();
    care_system.last_status_report = HAL_GetTick();
    care_system.last_location_update = HAL_GetTick();
    care_system.system_initialized = 1;
    
    my_printf(&huart1, "=== System Initialization Complete ===\r\n");
    my_printf(&huart1, "Commands: 'status', 'location', 'calibrate', 'test', 'emergency', 'reset'\r\n");
}

/**
 * @brief 更新位置信息 (模拟GPS数据)
 */
static void ElderlyCareSystem_UpdateLocation(void)
{
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - care_system.last_location_update > LOCATION_MOCK_UPDATE_MS) {
        // 模拟轻微的位置变化 (实际应用中从GPS模块读取)
        static float offset = 0.0f;
        offset += 0.0001f; // 模拟移动
        
        double lat = mock_latitude + (sinf(offset) * 0.001f);  // ±100米范围内变化
        double lon = mock_longitude + (cosf(offset) * 0.001f);
        float alt = 50.0f + (sinf(offset * 2) * 10.0f);       // 海拔变化
        float accuracy = 5.0f + (rand() % 10);                // 精度5-15米
        
        LocationService_UpdateLocation(lat, lon, alt, accuracy);
        care_system.last_location_update = current_time;
        
        my_printf(&huart1, "Location updated (simulated GPS)\r\n");
    }
}

/**
 * @brief 处理摔倒检测
 */
static void ElderlyCareSystem_HandleFallDetection(void)
{
    // 这里集成之前创建的摔倒检测功能
    // 由于摔倒检测代码在mpu6050_example.c中，这里调用相关函数
    
    static uint8_t last_fall_state = 0;
    uint8_t current_fall_state = 0; // 这里应该调用实际的摔倒检测函数
    
    // 检测摔倒状态变化
    if (current_fall_state && !last_fall_state) {
        // 摔倒被检测到
        care_system.fall_detection_count++;
        
        my_printf(&huart1, "FALL DETECTED! Sending emergency alert...\r\n");
        
        // 发送紧急警报到华为云
        LocationService_TriggerEmergency(EMERGENCY_FALL_DETECTED, 
                                        "Fall detected by MPU6050 sensor");
        
        care_system.emergency_alerts_sent++;
        
        // 生成紧急地图URL
        char emergency_map_url[256];
        LocationService_GenerateEmergencyMapURL(emergency_map_url, sizeof(emergency_map_url));
        my_printf(&huart1, "Emergency Map: %s\r\n", emergency_map_url);
    }
    
    last_fall_state = current_fall_state;
}

/**
 * @brief 发送状态报告
 */
static void ElderlyCareSystem_SendStatusReport(void)
{
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - care_system.last_status_report > STATUS_REPORT_INTERVAL_MS) {
        care_system.total_runtime_seconds = (current_time - care_system.system_start_time) / 1000;
        
        my_printf(&huart1, "\r\n=== Elderly Care System Status Report ===\r\n");
        my_printf(&huart1, "Runtime: %lu seconds\r\n", care_system.total_runtime_seconds);
        my_printf(&huart1, "MPU6050: %s\r\n", care_system.mpu6050_ready ? "Ready" : "Not Ready");
        my_printf(&huart1, "Location Service: %s\r\n", care_system.location_service_ready ? "Ready" : "Not Ready");
        my_printf(&huart1, "Cloud Connection: %s\r\n", care_system.cloud_connected ? "Connected" : "Disconnected");
        my_printf(&huart1, "Fall Detections: %lu\r\n", care_system.fall_detection_count);
        my_printf(&huart1, "Emergency Alerts: %lu\r\n", care_system.emergency_alerts_sent);
        
        if (LocationService_IsLocationValid()) {
            GPS_Coordinate_t* loc = LocationService_GetCurrentLocation();
            my_printf(&huart1, "Current Location: %.6f, %.6f\r\n", loc->latitude, loc->longitude);
            
            char map_url[256];
            LocationService_GenerateMapURL(map_url, sizeof(map_url));
            my_printf(&huart1, "Map URL: %s\r\n", map_url);
        }
        
        my_printf(&huart1, "==========================================\r\n\r\n");
        
        care_system.last_status_report = current_time;
    }
}

/**
 * @brief 处理串口命令
 */
static void ElderlyCareSystem_ProcessCommands(void)
{
    // 这里可以处理从串口接收到的命令
    // 例如：校准、测试、紧急重置等
    
    // 示例命令处理逻辑 (需要与串口接收结合)
    /*
    if (received_command == "status") {
        LocationService_PrintStatus();
    } else if (received_command == "location") {
        LocationService_PrintLocationHistory();
    } else if (received_command == "calibrate") {
        MPU6050_App_Calibrate(1000);
    } else if (received_command == "emergency") {
        LocationService_TriggerEmergency(EMERGENCY_SOS_BUTTON, "Manual SOS triggered");
    } else if (received_command == "reset") {
        LocationService_ClearEmergency();
    }
    */
}

/**
 * @brief 老人关爱系统主任务
 * 应该在调度器中以10Hz频率调用
 */
void ElderlyCareSystem_Task(void)
{
    // 首次运行时初始化
    if (!care_system.system_initialized) {
        ElderlyCareSystem_Init();
        return;
    }
    
    // 更新位置信息
    ElderlyCareSystem_UpdateLocation();
    
    // 处理摔倒检测
    ElderlyCareSystem_HandleFallDetection();
    
    // 运行定位服务任务
    LocationService_Task();
    
    // 发送定期状态报告
    ElderlyCareSystem_SendStatusReport();
    
    // 处理命令
    ElderlyCareSystem_ProcessCommands();
}

/**
 * @brief 获取系统状态
 */
uint8_t ElderlyCareSystem_IsReady(void)
{
    return care_system.system_initialized && 
           care_system.mpu6050_ready && 
           care_system.location_service_ready;
}

/**
 * @brief 手动触发紧急事件 (用于测试)
 */
void ElderlyCareSystem_TriggerTestEmergency(void)
{
    my_printf(&huart1, "Triggering test emergency...\r\n");
    LocationService_TriggerEmergency(EMERGENCY_SOS_BUTTON, "Test emergency triggered manually");
    care_system.emergency_alerts_sent++;
}
