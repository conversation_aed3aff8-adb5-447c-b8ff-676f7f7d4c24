/**
 * @file mpu6050_example.c
 * @brief MPU6050老人摔倒检测系统
 *
 * 基于MPU6050传感器实现老人摔倒检测功能
 * 综合分析加速度、角速度和姿态角变化
 */

#include "mpu6050_app.h"

/* 摔倒检测参数配置 */
#define FALL_ACCEL_THRESHOLD_HIGH    2.5f    // 高加速度阈值 (g) - 检测冲击
#define FALL_ACCEL_THRESHOLD_LOW     0.5f    // 低加速度阈值 (g) - 检测自由落体
#define FALL_GYRO_THRESHOLD          200.0f  // 角速度阈值 (°/s) - 检测旋转
#define FALL_ANGLE_THRESHOLD         60.0f   // 姿态角阈值 (°) - 检测倾倒
#define FALL_DETECTION_TIME_MS       2000    // 摔倒检测时间窗口 (ms)
#define FALL_CONFIRM_TIME_MS         5000    // 摔倒确认时间 (ms)
#define STABLE_TIME_THRESHOLD_MS     3000    // 稳定状态时间阈值 (ms)

/* 摔倒检测状态 */
typedef enum {
    FALL_STATE_NORMAL = 0,      // 正常状态
    FALL_STATE_PRE_IMPACT,      // 预冲击状态 (自由落体)
    FALL_STATE_IMPACT,          // 冲击状态
    FALL_STATE_POST_IMPACT,     // 后冲击状态
    FALL_STATE_FALLEN,          // 已摔倒状态
    FALL_STATE_CONFIRMED        // 摔倒确认状态
} FallDetectionState_t;

/* 摔倒检测数据结构 */
typedef struct {
    FallDetectionState_t state;         // 当前状态
    uint32_t state_start_time;          // 状态开始时间
    uint32_t last_movement_time;        // 最后活动时间

    float max_accel_magnitude;          // 最大加速度幅值
    float min_accel_magnitude;          // 最小加速度幅值
    float max_gyro_magnitude;           // 最大角速度幅值

    uint8_t fall_detected;              // 摔倒检测标志
    uint8_t fall_confirmed;             // 摔倒确认标志
    uint32_t fall_detection_time;       // 摔倒检测时间

    // 统计信息
    uint32_t total_detections;          // 总检测次数
    uint32_t false_alarms;              // 误报次数
} FallDetection_t;

/* 全局摔倒检测实例 */
static FallDetection_t fall_detector = {0};

/* 私有函数声明 */
static void FallDetection_Init(void);
static void FallDetection_UpdateState(void);
static float FallDetection_CalculateAccelMagnitude(MPU6050_Data_t *data);
static float FallDetection_CalculateGyroMagnitude(MPU6050_Data_t *data);
static uint8_t FallDetection_CheckMovement(MPU6050_Data_t *data);
static void FallDetection_HandleAlarm(void);
static void FallDetection_ResetDetection(void);

/**
 * @brief 初始化摔倒检测系统
 */
static void FallDetection_Init(void)
{
    memset(&fall_detector, 0, sizeof(FallDetection_t));
    fall_detector.state = FALL_STATE_NORMAL;
    fall_detector.state_start_time = HAL_GetTick();
    fall_detector.last_movement_time = HAL_GetTick();

    my_printf(&huart1, "Fall Detection System Initialized\r\n");
}

/**
 * @brief 计算加速度向量的模长
 */
static float FallDetection_CalculateAccelMagnitude(MPU6050_Data_t *data)
{
    return sqrtf(data->accel_x * data->accel_x +
                 data->accel_y * data->accel_y +
                 data->accel_z * data->accel_z);
}

/**
 * @brief 计算角速度向量的模长
 */
static float FallDetection_CalculateGyroMagnitude(MPU6050_Data_t *data)
{
    return sqrtf(data->gyro_x * data->gyro_x +
                 data->gyro_y * data->gyro_y +
                 data->gyro_z * data->gyro_z);
}

/**
 * @brief 检测是否有运动
 */
static uint8_t FallDetection_CheckMovement(MPU6050_Data_t *data)
{
    float accel_magnitude = FallDetection_CalculateAccelMagnitude(data);
    float gyro_magnitude = FallDetection_CalculateGyroMagnitude(data);

    // 检测运动阈值
    if (fabsf(accel_magnitude - 1.0f) > 0.2f || gyro_magnitude > 10.0f) {
        fall_detector.last_movement_time = HAL_GetTick();
        return 1;
    }

    return 0;
}

/**
 * @brief 摔倒检测状态机更新
 */
static void FallDetection_UpdateState(void)
{
    if (!MPU6050_App_IsReady() || !MPU6050_App_IsCalibrated()) {
        return;
    }

    MPU6050_Data_t *data = MPU6050_App_GetData();
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    uint32_t current_time = HAL_GetTick();

    // 计算关键参数
    float accel_magnitude = FallDetection_CalculateAccelMagnitude(data);
    float gyro_magnitude = FallDetection_CalculateGyroMagnitude(data);
    float max_angle = fmaxf(fabsf(attitude->roll), fabsf(attitude->pitch));

    // 更新最大值记录
    if (accel_magnitude > fall_detector.max_accel_magnitude) {
        fall_detector.max_accel_magnitude = accel_magnitude;
    }
    if (accel_magnitude < fall_detector.min_accel_magnitude) {
        fall_detector.min_accel_magnitude = accel_magnitude;
    }
    if (gyro_magnitude > fall_detector.max_gyro_magnitude) {
        fall_detector.max_gyro_magnitude = gyro_magnitude;
    }

    // 检测运动
    FallDetection_CheckMovement(data);

    // 状态机逻辑
    switch (fall_detector.state) {
        case FALL_STATE_NORMAL:
            // 重置检测参数
            fall_detector.max_accel_magnitude = accel_magnitude;
            fall_detector.min_accel_magnitude = accel_magnitude;
            fall_detector.max_gyro_magnitude = gyro_magnitude;

            // 检测自由落体 (低加速度)
            if (accel_magnitude < FALL_ACCEL_THRESHOLD_LOW) {
                fall_detector.state = FALL_STATE_PRE_IMPACT;
                fall_detector.state_start_time = current_time;
                my_printf(&huart1, "Fall Detection: Pre-impact detected\r\n");
            }
            // 直接检测高冲击
            else if (accel_magnitude > FALL_ACCEL_THRESHOLD_HIGH) {
                fall_detector.state = FALL_STATE_IMPACT;
                fall_detector.state_start_time = current_time;
                my_printf(&huart1, "Fall Detection: Direct impact detected\r\n");
            }
            break;

        case FALL_STATE_PRE_IMPACT:
            // 在预冲击状态下检测冲击
            if (accel_magnitude > FALL_ACCEL_THRESHOLD_HIGH) {
                fall_detector.state = FALL_STATE_IMPACT;
                fall_detector.state_start_time = current_time;
                my_printf(&huart1, "Fall Detection: Impact after free fall\r\n");
            }
            // 超时返回正常状态
            else if (current_time - fall_detector.state_start_time > 1000) {
                fall_detector.state = FALL_STATE_NORMAL;
            }
            break;

        case FALL_STATE_IMPACT:
            // 冲击后检测姿态变化和角速度
            if (gyro_magnitude > FALL_GYRO_THRESHOLD || max_angle > FALL_ANGLE_THRESHOLD) {
                fall_detector.state = FALL_STATE_POST_IMPACT;
                fall_detector.state_start_time = current_time;
                my_printf(&huart1, "Fall Detection: Post-impact motion detected\r\n");
            }
            // 超时返回正常状态
            else if (current_time - fall_detector.state_start_time > 1000) {
                fall_detector.state = FALL_STATE_NORMAL;
            }
            break;

        case FALL_STATE_POST_IMPACT:
            // 检测是否稳定在倾倒状态
            if (max_angle > FALL_ANGLE_THRESHOLD && gyro_magnitude < 50.0f) {
                fall_detector.state = FALL_STATE_FALLEN;
                fall_detector.state_start_time = current_time;
                fall_detector.fall_detected = 1;
                fall_detector.fall_detection_time = current_time;
                fall_detector.total_detections++;
                my_printf(&huart1, "FALL DETECTED! Person may have fallen!\r\n");
                FallDetection_HandleAlarm();
            }
            // 超时或恢复正常姿态
            else if (current_time - fall_detector.state_start_time > 2000 || max_angle < 30.0f) {
                fall_detector.state = FALL_STATE_NORMAL;
            }
            break;

        case FALL_STATE_FALLEN:
            // 检查是否长时间无活动 (确认摔倒)
            if (current_time - fall_detector.last_movement_time > STABLE_TIME_THRESHOLD_MS) {
                fall_detector.state = FALL_STATE_CONFIRMED;
                fall_detector.fall_confirmed = 1;
                my_printf(&huart1, "FALL CONFIRMED! No movement detected for %d seconds!\r\n",
                         STABLE_TIME_THRESHOLD_MS / 1000);
            }
            // 检测到活动，可能是误报或已恢复
            else if (max_angle < 30.0f && gyro_magnitude > 20.0f) {
                my_printf(&huart1, "Fall Detection: Recovery detected, false alarm\r\n");
                fall_detector.false_alarms++;
                FallDetection_ResetDetection();
            }
            break;

        case FALL_STATE_CONFIRMED:
            // 确认摔倒状态，等待手动重置或检测到恢复
            if (max_angle < 20.0f && gyro_magnitude > 30.0f) {
                my_printf(&huart1, "Fall Detection: Recovery from confirmed fall\r\n");
                FallDetection_ResetDetection();
            }
            break;
    }
}

/**
 * @brief 处理摔倒报警
 */
static void FallDetection_HandleAlarm(void)
{
    // LED指示 (如果有LED)
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_SET);

    // 串口报警信息
    my_printf(&huart1, "=== FALL ALARM ===\r\n");
    my_printf(&huart1, "Time: %lu ms\r\n", fall_detector.fall_detection_time);
    my_printf(&huart1, "Max Accel: %.2f g\r\n", fall_detector.max_accel_magnitude);
    my_printf(&huart1, "Min Accel: %.2f g\r\n", fall_detector.min_accel_magnitude);
    my_printf(&huart1, "Max Gyro: %.2f °/s\r\n", fall_detector.max_gyro_magnitude);

    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    my_printf(&huart1, "Current Attitude: Roll=%.1f° Pitch=%.1f°\r\n",
              attitude->roll, attitude->pitch);
    my_printf(&huart1, "==================\r\n");

    // 这里可以添加其他报警方式：
    // - 蜂鸣器
    // - 无线通信发送报警
    // - 存储事件日志
}

/**
 * @brief 重置摔倒检测
 */
static void FallDetection_ResetDetection(void)
{
    fall_detector.state = FALL_STATE_NORMAL;
    fall_detector.state_start_time = HAL_GetTick();
    fall_detector.fall_detected = 0;
    fall_detector.fall_confirmed = 0;
    fall_detector.max_accel_magnitude = 1.0f;
    fall_detector.min_accel_magnitude = 1.0f;
    fall_detector.max_gyro_magnitude = 0.0f;

    // 关闭LED指示
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_RESET);

    my_printf(&huart1, "Fall Detection Reset\r\n");
}

/**
 * @brief 主要的摔倒检测任务函数
 * 应该在主循环中周期性调用 (建议20-50Hz)
 */
void FallDetection_Task(void)
{
    static uint8_t initialized = 0;

    // 首次运行时初始化
    if (!initialized) {
        FallDetection_Init();
        initialized = 1;
        return;
    }

    // 检查MPU6050是否就绪
    if (!MPU6050_App_IsReady()) {
        return;
    }

    // 更新检测状态
    FallDetection_UpdateState();
}

/**
 * @brief 获取摔倒检测状态
 */
uint8_t FallDetection_IsFallDetected(void)
{
    return fall_detector.fall_detected;
}

/**
 * @brief 获取摔倒确认状态
 */
uint8_t FallDetection_IsFallConfirmed(void)
{
    return fall_detector.fall_confirmed;
}

/**
 * @brief 手动重置摔倒检测 (用于测试或恢复)
 */
void FallDetection_ManualReset(void)
{
    my_printf(&huart1, "Manual reset triggered\r\n");
    FallDetection_ResetDetection();
}

/**
 * @brief 打印摔倒检测统计信息
 */
void FallDetection_PrintStats(void)
{
    my_printf(&huart1, "=== Fall Detection Statistics ===\r\n");
    my_printf(&huart1, "Current State: %d\r\n", fall_detector.state);
    my_printf(&huart1, "Total Detections: %lu\r\n", fall_detector.total_detections);
    my_printf(&huart1, "False Alarms: %lu\r\n", fall_detector.false_alarms);
    my_printf(&huart1, "Last Movement: %lu ms ago\r\n",
              HAL_GetTick() - fall_detector.last_movement_time);

    if (fall_detector.fall_detected) {
        my_printf(&huart1, "Fall Status: DETECTED\r\n");
        if (fall_detector.fall_confirmed) {
            my_printf(&huart1, "Fall Confirmed: YES\r\n");
        }
    } else {
        my_printf(&huart1, "Fall Status: NORMAL\r\n");
    }
    my_printf(&huart1, "================================\r\n");
}

/**
 * @brief 摔倒检测系统测试函数
 */
void FallDetection_Test(void)
{
    my_printf(&huart1, "=== Fall Detection System Test ===\r\n");

    // 检查MPU6050状态
    if (!MPU6050_App_IsConnected()) {
        my_printf(&huart1, "ERROR: MPU6050 not connected!\r\n");
        return;
    }

    if (!MPU6050_App_IsCalibrated()) {
        my_printf(&huart1, "WARNING: MPU6050 not calibrated!\r\n");
        my_printf(&huart1, "Please calibrate first for accurate detection.\r\n");
    }

    // 显示当前传感器数据
    MPU6050_Data_t *data = MPU6050_App_GetData();
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();

    float accel_mag = FallDetection_CalculateAccelMagnitude(data);
    float gyro_mag = FallDetection_CalculateGyroMagnitude(data);

    my_printf(&huart1, "Current Sensor Data:\r\n");
    my_printf(&huart1, "Accel Magnitude: %.3f g\r\n", accel_mag);
    my_printf(&huart1, "Gyro Magnitude: %.3f °/s\r\n", gyro_mag);
    my_printf(&huart1, "Roll: %.1f°, Pitch: %.1f°\r\n", attitude->roll, attitude->pitch);

    // 显示检测阈值
    my_printf(&huart1, "\r\nDetection Thresholds:\r\n");
    my_printf(&huart1, "High Accel: %.1f g\r\n", FALL_ACCEL_THRESHOLD_HIGH);
    my_printf(&huart1, "Low Accel: %.1f g\r\n", FALL_ACCEL_THRESHOLD_LOW);
    my_printf(&huart1, "Gyro: %.1f °/s\r\n", FALL_GYRO_THRESHOLD);
    my_printf(&huart1, "Angle: %.1f°\r\n", FALL_ANGLE_THRESHOLD);

    my_printf(&huart1, "==================================\r\n");
}

/**
 * @brief 模拟摔倒测试 (仅用于开发测试)
 */
void FallDetection_SimulateTest(void)
{
    my_printf(&huart1, "Simulating fall detection...\r\n");
    my_printf(&huart1, "Please perform the following actions:\r\n");
    my_printf(&huart1, "1. Hold device steady (normal state)\r\n");
    my_printf(&huart1, "2. Drop device quickly (free fall)\r\n");
    my_printf(&huart1, "3. Catch and shake (impact)\r\n");
    my_printf(&huart1, "4. Tilt device >60° and keep still (fallen)\r\n");
    my_printf(&huart1, "Watch for detection messages...\r\n");
}

/**
 * @brief 你的主要逻辑层代码 - 老人摔倒检测系统
 *
 * 使用方法：
 * 1. 确保MPU6050已初始化和校准
 * 2. 在主循环中调用 FallDetection_Task()
 * 3. 根据需要查询检测状态
 */
void Your_Logic_Code_Example(void)
{
    // 主要的摔倒检测任务 (应该在调度器中以20-50Hz频率调用)
    FallDetection_Task();

    // 示例：检查摔倒状态并处理
    static uint32_t last_status_print = 0;
    uint32_t current_time = HAL_GetTick();

    // 每5秒打印一次状态 (可选)
    if (current_time - last_status_print > 5000) {
        // FallDetection_PrintStats();  // 取消注释以启用状态打印
        last_status_print = current_time;
    }

    // 检查摔倒检测结果
    if (FallDetection_IsFallDetected()) {
        // 摔倒被检测到，可以在这里添加你的处理逻辑

        if (FallDetection_IsFallConfirmed()) {
            // 摔倒已确认，执行紧急处理
            // 例如：发送紧急信号、拨打电话、记录事件等

            // 示例：通过串口2发送报警信息
            // Uart2_Printf(&huart2, "EMERGENCY: Fall confirmed at %lu\r\n", current_time);
        }
    }

    // 其他你的逻辑代码...
    // 例如：
    // - 定期发送心跳信号
    // - 处理用户输入
    // - 数据记录和分析
    // - 与其他系统通信
}

/**
 * @brief MPU6050使用示例 - 基本数据读取
 */
void MPU6050_Example_BasicRead(void)
{
    // 检查MPU6050是否就绪
    if (!MPU6050_App_IsReady()) {
        my_printf(&huart1, "MPU6050 not ready!\r\n");
        return;
    }
    
    // 获取处理后的数据
    MPU6050_Data_t *data = MPU6050_App_GetData();
    
    // 打印加速度计数据
    my_printf(&huart1, "Accel: X=%.3f, Y=%.3f, Z=%.3f (g)\r\n", 
              data->accel_x, data->accel_y, data->accel_z);
    
    // 打印陀螺仪数据
    my_printf(&huart1, "Gyro: X=%.3f, Y=%.3f, Z=%.3f (°/s)\r\n", 
              data->gyro_x, data->gyro_y, data->gyro_z);
    
    // 打印温度数据
    my_printf(&huart1, "Temperature: %.2f °C\r\n", data->temperature);
}

/**
 * @brief MPU6050使用示例 - 姿态角读取
 */
void MPU6050_Example_AttitudeRead(void)
{
    // 检查是否已校准
    if (!MPU6050_App_IsCalibrated()) {
        my_printf(&huart1, "MPU6050 not calibrated! Please calibrate first.\r\n");
        return;
    }
    
    // 获取姿态角数据
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    
    // 打印姿态角
    my_printf(&huart1, "Attitude: Roll=%.2f°, Pitch=%.2f°, Yaw=%.2f°\r\n", 
              attitude->roll, attitude->pitch, attitude->yaw);
}

/**
 * @brief MPU6050使用示例 - 校准
 */
void MPU6050_Example_Calibrate(void)
{
    my_printf(&huart1, "Starting MPU6050 calibration...\r\n");
    my_printf(&huart1, "Please keep the device still!\r\n");
    
    // 执行校准 (1000个样本)
    if (MPU6050_App_Calibrate(1000) == 0) {
        my_printf(&huart1, "Calibration completed successfully!\r\n");
    } else {
        my_printf(&huart1, "Calibration failed!\r\n");
    }
}

/**
 * @brief MPU6050使用示例 - 配置修改
 */
void MPU6050_Example_SetConfig(void)
{
    // 设置陀螺仪量程为±500°/s，加速度计量程为±4g
    if (MPU6050_App_SetConfig(MPU6050_GYRO_RANGE_500DPS, MPU6050_ACCEL_RANGE_4G) == 0) {
        my_printf(&huart1, "MPU6050 configuration updated!\r\n");
    } else {
        my_printf(&huart1, "Failed to update MPU6050 configuration!\r\n");
    }
}

/**
 * @brief MPU6050使用示例 - 运动检测
 */
void MPU6050_Example_MotionDetection(void)
{
    static float motion_threshold = 0.1f;  // 运动阈值 (g)
    
    MPU6050_Data_t *data = MPU6050_App_GetData();
    
    // 计算加速度向量的模长
    float accel_magnitude = sqrtf(data->accel_x * data->accel_x + 
                                  data->accel_y * data->accel_y + 
                                  data->accel_z * data->accel_z);
    
    // 检测是否有运动 (偏离1g)
    if (fabsf(accel_magnitude - 1.0f) > motion_threshold) {
        my_printf(&huart1, "Motion detected! Magnitude: %.3f g\r\n", accel_magnitude);
    }
}

/**
 * @brief MPU6050使用示例 - 倾斜检测
 */
void MPU6050_Example_TiltDetection(void)
{
    static float tilt_threshold = 30.0f;  // 倾斜阈值 (度)
    
    if (!MPU6050_App_IsCalibrated()) {
        return;
    }
    
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    
    // 检测是否倾斜
    if (fabsf(attitude->roll) > tilt_threshold || fabsf(attitude->pitch) > tilt_threshold) {
        my_printf(&huart1, "Device tilted! Roll: %.1f°, Pitch: %.1f°\r\n", 
                  attitude->roll, attitude->pitch);
    }
}

/**
 * @brief 你的逻辑层代码示例
 * 在这里编写你的具体应用逻辑
 */
void Your_Logic_Code_Example(void)
{
    // 示例1: 基本数据读取
    // MPU6050_Example_BasicRead();
    
    // 示例2: 姿态角读取
    // MPU6050_Example_AttitudeRead();
    
    // 示例3: 运动检测
    // MPU6050_Example_MotionDetection();
    
    // 示例4: 倾斜检测
    // MPU6050_Example_TiltDetection();
    
    // 在这里添加你的具体逻辑...
    
    /* 例如：
     * 1. 根据姿态角控制电机
     * 2. 根据加速度检测碰撞
     * 3. 实现平衡控制算法
     * 4. 数据记录和分析
     * 等等...
     */
}
