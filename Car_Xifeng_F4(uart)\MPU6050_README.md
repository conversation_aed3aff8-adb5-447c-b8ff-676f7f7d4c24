# MPU6050 IIC通信框架使用说明

## 概述

这是一个完整的MPU6050 IIC通信框架，采用分层架构设计，让你只需要专注于逻辑层的开发。

## 框架结构

```
MPU6050框架
├── 驱动层 (Components/MPU6050/)
│   ├── mpu6050_driver.h    # 底层IIC通信和寄存器操作
│   └── mpu6050_driver.c
├── 应用层 (APP/)
│   ├── mpu6050_app.h       # 高层API和数据处理
│   ├── mpu6050_app.c
│   └── mpu6050_example.c   # 使用示例
└── 配置文件
    ├── MyDefine.h          # 头文件包含
    └── scheduler.c         # 任务调度
```

## 硬件配置

- **IIC接口**: I2C2
- **引脚**: PB10(SCL), PB11(SDA)
- **时钟频率**: 400kHz
- **设备地址**: 0xD0 (AD0=0)

## 主要功能

### 1. 数据结构

```c
// 原始数据
typedef struct {
    int16_t accel_x_raw, accel_y_raw, accel_z_raw;
    int16_t temp_raw;
    int16_t gyro_x_raw, gyro_y_raw, gyro_z_raw;
} MPU6050_RawData_t;

// 处理后数据
typedef struct {
    float accel_x, accel_y, accel_z;    // 加速度 (g)
    float temperature;                   // 温度 (°C)
    float gyro_x, gyro_y, gyro_z;       // 角速度 (°/s)
} MPU6050_Data_t;

// 姿态角
typedef struct {
    float roll, pitch, yaw;             // 姿态角 (°)
} MPU6050_Attitude_t;
```

### 2. 核心API

```c
// 初始化
uint8_t MPU6050_App_Init(void);

// 数据读取
uint8_t MPU6050_App_ReadData(void);
void MPU6050_App_Task(void);

// 校准
uint8_t MPU6050_App_Calibrate(uint16_t samples);

// 数据获取
MPU6050_Data_t* MPU6050_App_GetData(void);
MPU6050_Attitude_t* MPU6050_App_GetAttitude(void);

// 状态查询
uint8_t MPU6050_App_IsReady(void);
uint8_t MPU6050_App_IsCalibrated(void);
uint8_t MPU6050_App_IsConnected(void);
```

## 使用步骤

### 1. 系统已自动初始化
框架已集成到调度器中，系统启动时会自动：
- 初始化IIC接口
- 检测MPU6050连接
- 配置默认参数
- 启动数据采集任务 (50Hz)

### 2. 校准设备 (首次使用)
```c
// 在设备静止状态下执行校准
if (MPU6050_App_Calibrate(1000) == 0) {
    // 校准成功
}
```

### 3. 读取数据
```c
// 获取处理后的数据
MPU6050_Data_t *data = MPU6050_App_GetData();
float accel_x = data->accel_x;  // 加速度X轴 (g)
float gyro_z = data->gyro_z;    // 角速度Z轴 (°/s)

// 获取姿态角 (需要先校准)
MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
float roll = attitude->roll;    // 横滚角 (°)
float pitch = attitude->pitch;  // 俯仰角 (°)
```

## 逻辑层开发示例

### 1. 运动检测
```c
void MotionDetection(void) {
    MPU6050_Data_t *data = MPU6050_App_GetData();
    
    float magnitude = sqrtf(data->accel_x * data->accel_x + 
                           data->accel_y * data->accel_y + 
                           data->accel_z * data->accel_z);
    
    if (fabsf(magnitude - 1.0f) > 0.1f) {
        // 检测到运动
    }
}
```

### 2. 倾斜检测
```c
void TiltDetection(void) {
    if (!MPU6050_App_IsCalibrated()) return;
    
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    
    if (fabsf(attitude->roll) > 30.0f || fabsf(attitude->pitch) > 30.0f) {
        // 设备倾斜超过30度
    }
}
```

### 3. 平衡控制
```c
void BalanceControl(void) {
    MPU6050_Attitude_t *attitude = MPU6050_App_GetAttitude();
    
    // PID控制算法
    float error = attitude->pitch;  // 俯仰角误差
    float output = PID_Calculate(error);
    
    // 控制电机
    Motor_SetSpeed(output);
}
```

## 配置选项

### 1. 量程设置
```c
// 设置陀螺仪±500°/s，加速度计±4g
MPU6050_App_SetConfig(MPU6050_GYRO_RANGE_500DPS, MPU6050_ACCEL_RANGE_4G);
```

### 2. 采样频率
在scheduler.c中修改任务周期：
```c
{MPU6050_App_Task, 20, 0},  // 20ms = 50Hz
```

## 特性

- ✅ **即插即用**: 自动初始化和配置
- ✅ **数据融合**: 卡尔曼滤波器融合加速度计和陀螺仪
- ✅ **自动校准**: 消除传感器偏差
- ✅ **姿态解算**: 实时计算Roll/Pitch/Yaw角度
- ✅ **错误处理**: 完善的错误检测和恢复机制
- ✅ **高性能**: 优化的IIC通信和数据处理

## 注意事项

1. **首次使用需要校准**: 在设备静止状态下调用校准函数
2. **姿态角需要校准**: 只有校准后才能获得准确的姿态角
3. **Yaw角漂移**: 没有磁力计，Yaw角会有累积误差
4. **IIC地址**: 默认使用0xD0，如果AD0=1则需要修改为0xD2

现在你只需要在逻辑层编写具体的应用代码即可！
