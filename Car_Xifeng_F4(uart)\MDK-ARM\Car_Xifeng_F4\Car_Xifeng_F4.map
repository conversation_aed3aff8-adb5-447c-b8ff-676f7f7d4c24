Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM4_Init) for MX_TIM4_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C2_Init) for MX_I2C2_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART4_Init) for MX_UART4_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Init) for Scheduler_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Run) for Scheduler_Run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C2_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    i2c.o(.text.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C2_Init) refers to i2c.o(.text.MX_I2C2_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM4_Init) refers to tim.o(.bss.htim4) for htim4
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM4_Init) refers to tim.o(.text.MX_TIM4_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.huart4) for huart4
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART4_Init) refers to usart.o(.text.MX_UART4_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART3_UART_Init) refers to usart.o(.bss.huart3) for huart3
    usart.o(.text.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART3_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART3_UART_Init) refers to usart.o(.text.MX_USART3_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART6_UART_Init) refers to usart.o(.bss.huart6) for huart6
    usart.o(.text.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART6_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART6_UART_Init) refers to usart.o(.text.MX_USART6_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(.ARM.exidx.text.my_printf) refers to usart.o(.text.my_printf) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream1_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to usart.o(.bss.huart3) for huart3
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.huart4) for huart4
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to usart.o(.bss.huart6) for huart6
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    encoder_driver.o(.text.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init) refers to encoder_driver.o(.text.Encoder_Driver_Init) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update) refers to encoder_driver.o(.text.Encoder_Driver_Update) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.Ping) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Digtal) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize) refers to hardware_iic.o(.text.IIC_Anolog_Normalize) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Offset) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Offset) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset) refers to hardware_iic.o(.text.IIC_Get_Offset) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status) refers to ringbuffer.o(.text.rt_ringbuffer_status) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put) refers to ringbuffer.o(.text.rt_ringbuffer_put) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.text.rt_ringbuffer_put_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get) refers to ringbuffer.o(.text.rt_ringbuffer_get) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek) refers to ringbuffer.o(.text.rt_ringbuffer_peek) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.text.rt_ringbuffer_putchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.text.rt_ringbuffer_putchar_force) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.text.rt_ringbuffer_getchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for [Anonymous Symbol]
    uart_driver.o(.text.Uart_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.text.Uart_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_driver.o(.ARM.exidx.text.Uart_Printf) refers to uart_driver.o(.text.Uart_Printf) for [Anonymous Symbol]
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) for HAL_UARTEx_RxEventCallback_UART2
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.huart1) for huart1
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) for HAL_UARTEx_RxEventCallback_UART3
    uart_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    uart2_driver.o(.text.Uart2_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart2_driver.o(.text.Uart2_Printf) refers to usart.o(.bss.huart1) for huart1
    uart2_driver.o(.text.Uart2_Printf) refers to usart.o(.text.my_printf) for my_printf
    uart2_driver.o(.text.Uart2_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart2_driver.o(.ARM.exidx.text.Uart2_Printf) refers to uart2_driver.o(.text.Uart2_Printf) for [Anonymous Symbol]
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to memseta.o(.text) for __aeabi_memclr4
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to usart.o(.bss.huart2) for huart2
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) for [Anonymous Symbol]
    uart3_driver.o(.text.Uart3_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart3_driver.o(.text.Uart3_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart3_driver.o(.ARM.exidx.text.Uart3_Printf) refers to uart3_driver.o(.text.Uart3_Printf) for [Anonymous Symbol]
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.bss.uart3_rx_dma_buffer) for uart3_rx_dma_buffer
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to memseta.o(.text) for __aeabi_memclr4
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to usart.o(.bss.huart3) for huart3
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    uart3_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) for [Anonymous Symbol]
    uart6_driver.o(.text.Uart6_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart6_driver.o(.text.Uart6_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart6_driver.o(.ARM.exidx.text.Uart6_Printf) refers to uart6_driver.o(.text.Uart6_Printf) for [Anonymous Symbol]
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.bss.uart6_rx_dma_buffer) for uart6_rx_dma_buffer
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.bss.uart6_ring_buffer) for uart6_ring_buffer
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to memseta.o(.text) for __aeabi_memclr4
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to usart.o(.bss.huart6) for huart6
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    uart6_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) for [Anonymous Symbol]
    scheduler.o(.text.System_Init) refers to uart_app.o(.text.Uart_Init) for Uart_Init
    scheduler.o(.text.System_Init) refers to uart2_app.o(.text.Uart2_Init) for Uart2_Init
    scheduler.o(.text.System_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for GPS_Virtual_Init
    scheduler.o(.text.System_Init) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    scheduler.o(.ARM.exidx.text.System_Init) refers to scheduler.o(.text.System_Init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Init) refers to uart_app.o(.text.Uart_Init) for Uart_Init
    scheduler.o(.text.Scheduler_Init) refers to uart2_app.o(.text.Uart2_Init) for Uart2_Init
    scheduler.o(.text.Scheduler_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for GPS_Virtual_Init
    scheduler.o(.text.Scheduler_Init) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    scheduler.o(.text.Scheduler_Init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.Scheduler_Init) refers to scheduler.o(.text.Scheduler_Init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.Scheduler_Run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.Scheduler_Run) refers to scheduler.o(.text.Scheduler_Run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_app.o(.text.Uart_Task) for Uart_Task
    scheduler.o(.data.scheduler_task) refers to uart2_app.o(.text.Uart2_Task) for Uart2_Task
    scheduler.o(.data.scheduler_task) refers to uart3_app.o(.text.Uart3_Task) for Uart3_Task
    scheduler.o(.data.scheduler_task) refers to gps_app.o(.text.GPS_Task) for GPS_Task
    scheduler.o(.data.scheduler_task) refers to gps_app.o(.text.GPS_Virtual_GenerateData) for GPS_Virtual_GenerateData
    scheduler.o(.data.scheduler_task) refers to esp01_app.o(.text.esp01_Task) for esp01_Task
    scheduler.o(.data.scheduler_task) refers to uart6_app.o(.text.Uart6_Task) for Uart6_Task
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.ring_buffer_input) for ring_buffer_input
    uart_app.o(.text.Uart_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(.text.Uart_Init) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    uart_app.o(.text.Uart_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(.text.Uart_Init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_app.o(.text.Uart_Init) refers to uart_app.o(.bss.Uart_Init.init_done) for Uart_Init.init_done
    uart_app.o(.text.Uart_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    uart_app.o(.text.Uart_Init) refers to usart.o(.text.my_printf) for my_printf
    uart_app.o(.ARM.exidx.text.Uart_Init) refers to uart_app.o(.text.Uart_Init) for [Anonymous Symbol]
    uart_app.o(.text.Uart_Task) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_app.o(.text.Uart_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart_app.o(.text.Uart_Task) refers to uart_app.o(.bss.Uart_Task.last_heartbeat) for Uart_Task.last_heartbeat
    uart_app.o(.text.Uart_Task) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.Uart_Task) refers to usart.o(.text.my_printf) for my_printf
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    uart_app.o(.text.Uart_Task) refers to uart_driver.o(.bss.uart_data_buffer) for uart_data_buffer
    uart_app.o(.text.Uart_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(.text.Uart_Task) refers to strncpy.o(.text) for strncpy
    uart_app.o(.text.Uart_Task) refers to strchr.o(.text) for strchr
    uart_app.o(.text.Uart_Task) refers to strlen.o(.text) for strlen
    uart_app.o(.text.Uart_Task) refers to uart_app.o(.rodata.str1.1) for .L.str.5
    uart_app.o(.text.Uart_Task) refers to memcmp.o(.text) for memcmp
    uart_app.o(.text.Uart_Task) refers to navigation_app.o(.text.Navigation_ProcessCommand) for Navigation_ProcessCommand
    uart_app.o(.text.Uart_Task) refers to navigation_app.o(.text.Navigation_StartNavigation) for Navigation_StartNavigation
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_GetState) for esp01_GetState
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_Reset) for esp01_Reset
    uart_app.o(.text.Uart_Task) refers to usart.o(.bss.huart2) for huart2
    uart_app.o(.text.Uart_Task) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    uart_app.o(.text.Uart_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_StartInit) for esp01_StartInit
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_NetworkDiagnostics) for esp01_NetworkDiagnostics
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_ForceReset) for esp01_ForceReset
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_EstablishTCPConnection) for esp01_EstablishTCPConnection
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_TryTCPWithIP) for esp01_TryTCPWithIP
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_SendLocationData) for esp01_SendLocationData
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_GetRealLocation) for esp01_GetRealLocation
    uart_app.o(.text.Uart_Task) refers to f2d.o(.text) for __aeabi_f2d
    uart_app.o(.text.Uart_Task) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    uart_app.o(.text.Uart_Task) refers to gps_app.o(.bss.Save_Data) for Save_Data
    uart_app.o(.ARM.exidx.text.Uart_Task) refers to uart_app.o(.text.Uart_Task) for [Anonymous Symbol]
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_ring_buffer_input) for uart2_ring_buffer_input
    uart2_app.o(.text.Uart2_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart2_app.o(.text.Uart2_Init) refers to usart.o(.bss.huart2) for huart2
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_app.o(.text.Uart2_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_app.o(.text.Uart2_Init) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_app.o(.ARM.exidx.text.Uart2_Init) refers to uart2_app.o(.text.Uart2_Init) for [Anonymous Symbol]
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_app.o(.text.Uart2_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart2_app.o(.text.Uart2_Task) refers to uart2_app.o(.bss.Uart2_Task.last_receive_time) for Uart2_Task.last_receive_time
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.huart2) for huart2
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_data_buffer) for uart2_data_buffer
    uart2_app.o(.text.Uart2_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.huart1) for huart1
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.text.my_printf) for my_printf
    uart2_app.o(.text.Uart2_Task) refers to strstr.o(.text) for strstr
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetConnected) for esp01_SetConnected
    uart2_app.o(.text.Uart2_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart2_app.o(.text.Uart2_Task) refers to uart2_app.o(.rodata.str1.1) for .L.str.5
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_StartInit) for esp01_StartInit
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetTCPConnected) for esp01_SetTCPConnected
    uart2_app.o(.text.Uart2_Task) refers to strchr.o(.text) for strchr
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetDataSendReady) for esp01_SetDataSendReady
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_ResetTCPState) for esp01_ResetTCPState
    uart2_app.o(.ARM.exidx.text.Uart2_Task) refers to uart2_app.o(.text.Uart2_Task) for [Anonymous Symbol]
    uart3_app.o(.text.Uart3_Init) refers to gps_app.o(.text.GPS_Init) for GPS_Init
    uart3_app.o(.text.Uart3_Init) refers to navigation_app.o(.text.Navigation_Init) for Navigation_Init
    uart3_app.o(.ARM.exidx.text.Uart3_Init) refers to uart3_app.o(.text.Uart3_Init) for [Anonymous Symbol]
    uart3_app.o(.text.Uart3_Task) refers to gps_app.o(.text.GPS_Task) for GPS_Task
    uart3_app.o(.text.Uart3_Task) refers to navigation_app.o(.text.Navigation_Task) for Navigation_Task
    uart3_app.o(.ARM.exidx.text.Uart3_Task) refers to uart3_app.o(.text.Uart3_Task) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Init) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_Init) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Init) refers to esp01_app.o(.rodata.str1.1) for .L.str
    esp01_app.o(.text.esp01_Init) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_Init) refers to esp01_app.o(.text.esp01_Init) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_Task) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Task) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    esp01_app.o(.text.esp01_Task) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.rodata.str1.1) for .L.str.19
    esp01_app.o(.text.esp01_Task) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.text.esp01_InitSequence) for esp01_InitSequence
    esp01_app.o(.ARM.exidx.text.esp01_Task) refers to esp01_app.o(.text.esp01_Task) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_InitSequence) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_InitSequence) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_InitSequence) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_InitSequence) refers to esp01_app.o(.rodata.str1.1) for .L.str.10
    esp01_app.o(.text.esp01_InitSequence) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_InitSequence) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_InitSequence) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    esp01_app.o(.text.esp01_InitSequence) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    esp01_app.o(.text.esp01_InitSequence) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    esp01_app.o(.text.esp01_InitSequence) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    esp01_app.o(.text.esp01_InitSequence) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.ARM.exidx.text.esp01_InitSequence) refers to esp01_app.o(.text.esp01_InitSequence) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_UploadGPSData) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    esp01_app.o(.text.esp01_UploadGPSData) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.text.esp01_UploadGPSData) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.rodata.str1.1) for .L.str.55
    esp01_app.o(.text.esp01_UploadGPSData) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.bss.url_params_buffer) for url_params_buffer
    esp01_app.o(.text.esp01_UploadGPSData) refers to printfa.o(i.__0snprintf) for __2snprintf
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.bss.http_request_buffer) for http_request_buffer
    esp01_app.o(.text.esp01_UploadGPSData) refers to strlen.o(.text) for strlen
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.text.esp01_SendDataWithRecovery) for esp01_SendDataWithRecovery
    esp01_app.o(.text.esp01_UploadGPSData) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_UploadGPSData) refers to esp01_app.o(.text.esp01_UploadGPSData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_CheckConnection) refers to esp01_app.o(.rodata.str1.1) for .L.str.19
    esp01_app.o(.text.esp01_CheckConnection) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_CheckConnection) refers to esp01_app.o(.text.esp01_CheckConnection) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_GetState) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.ARM.exidx.text.esp01_GetState) refers to esp01_app.o(.text.esp01_GetState) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetConnected) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SetConnected) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SetConnected) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_SetConnected) refers to esp01_app.o(.rodata.str1.1) for .L.str.2
    esp01_app.o(.text.esp01_SetConnected) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_SetConnected) refers to esp01_app.o(.text.esp01_EstablishTCPConnection) for esp01_EstablishTCPConnection
    esp01_app.o(.ARM.exidx.text.esp01_SetConnected) refers to esp01_app.o(.text.esp01_SetConnected) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.rodata.str1.1) for .L.str.29
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.text.esp01_TryTCPWithIP) for esp01_TryTCPWithIP
    esp01_app.o(.ARM.exidx.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.text.esp01_EstablishTCPConnection) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetTCPConnected) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SetTCPConnected) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SetTCPConnected) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_SetTCPConnected) refers to esp01_app.o(.text.esp01_SetTCPConnected) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetDataSendReady) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SetDataSendReady) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SetDataSendReady) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_SetDataSendReady) refers to esp01_app.o(.text.esp01_SetDataSendReady) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_ResetTCPState) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.ARM.exidx.text.esp01_ResetTCPState) refers to esp01_app.o(.text.esp01_ResetTCPState) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Reset) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_Reset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_Reset) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Reset) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_Reset) refers to esp01_app.o(.text.esp01_Reset) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendCommand) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_SendCommand) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_SendCommand) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SendCommand) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_SendCommand) refers to esp01_app.o(.text.esp01_SendCommand) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to esp01_app.o(.rodata.str1.1) for .L.str.29
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_CheckTCPStatus) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.ARM.exidx.text.esp01_CheckTCPStatus) refers to esp01_app.o(.text.esp01_CheckTCPStatus) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to esp01_app.o(.rodata.str1.1) for .L.str.32
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_TryTCPWithIP) refers to esp01_app.o(.text.esp01_TryTCPWithIP) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_GetRealLocation) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    esp01_app.o(.text.esp01_GetRealLocation) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_GetRealLocation) refers to esp01_app.o(.rodata.str1.1) for .L.str.54
    esp01_app.o(.text.esp01_GetRealLocation) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_GetRealLocation) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.ARM.exidx.text.esp01_GetRealLocation) refers to esp01_app.o(.text.esp01_GetRealLocation) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to esp01_app.o(.rodata.str1.1) for .L.str.100
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) for esp01_CheckAndRecoverTCPConnection
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_SendDataWithRecovery) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.ARM.exidx.text.esp01_SendDataWithRecovery) refers to esp01_app.o(.text.esp01_SendDataWithRecovery) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendLocationData) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    esp01_app.o(.ARM.exidx.text.esp01_SendLocationData) refers to esp01_app.o(.text.esp01_SendLocationData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SendNavigationData) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    esp01_app.o(.text.esp01_SendNavigationData) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.text.esp01_SendNavigationData) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.rodata.str1.1) for .L.str.55
    esp01_app.o(.text.esp01_SendNavigationData) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_SendNavigationData) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    esp01_app.o(.text.esp01_SendNavigationData) refers to navigation_app.o(.text.Navigation_CalculateDistance) for Navigation_CalculateDistance
    esp01_app.o(.text.esp01_SendNavigationData) refers to navigation_app.o(.text.Navigation_CalculateBearing) for Navigation_CalculateBearing
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.bss.url_params_buffer) for url_params_buffer
    esp01_app.o(.text.esp01_SendNavigationData) refers to printfa.o(i.__0snprintf) for __2snprintf
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.bss.http_request_buffer) for http_request_buffer
    esp01_app.o(.text.esp01_SendNavigationData) refers to strlen.o(.text) for strlen
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.text.esp01_SendDataWithRecovery) for esp01_SendDataWithRecovery
    esp01_app.o(.text.esp01_SendNavigationData) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_SendNavigationData) refers to esp01_app.o(.text.esp01_SendNavigationData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to esp01_app.o(.rodata.str1.1) for .L.str.29
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) refers to esp01_app.o(.text.esp01_EstablishTCPConnection) for esp01_EstablishTCPConnection
    esp01_app.o(.ARM.exidx.text.esp01_CheckAndRecoverTCPConnection) refers to esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.rodata.str1.1) for .L.str.102
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.text.esp01_NetworkDiagnostics) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_ForceReset) refers to esp01_app.o(.rodata.str1.1) for .L.str.44
    esp01_app.o(.text.esp01_ForceReset) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_ForceReset) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_ForceReset) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_ForceReset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.ARM.exidx.text.esp01_ForceReset) refers to esp01_app.o(.text.esp01_ForceReset) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_StartInit) refers to esp01_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_StartInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_StartInit) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_StartInit) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_StartInit) refers to esp01_app.o(.text.esp01_StartInit) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Init) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_ring_buffer_input) for uart3_ring_buffer_input
    gps_app.o(.text.GPS_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    gps_app.o(.text.GPS_Init) refers to usart.o(.bss.huart3) for huart3
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_rx_dma_buffer) for uart3_rx_dma_buffer
    gps_app.o(.text.GPS_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    gps_app.o(.text.GPS_Init) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    gps_app.o(.ARM.exidx.text.GPS_Init) refers to gps_app.o(.text.GPS_Init) for [Anonymous Symbol]
    gps_app.o(.text.clrStruct) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.clrStruct) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.ARM.exidx.text.clrStruct) refers to gps_app.o(.text.clrStruct) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Task) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    gps_app.o(.text.GPS_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    gps_app.o(.text.GPS_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Task) refers to uart3_driver.o(.bss.uart3_data_buffer) for uart3_data_buffer
    gps_app.o(.text.GPS_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.bss.USART_RX_BUF) for USART_RX_BUF
    gps_app.o(.text.GPS_Task) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Task) refers to memcpya.o(.text) for __aeabi_memcpy4
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.text.parseGpsBuffer) for parseGpsBuffer
    gps_app.o(.text.GPS_Task) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.rodata.str1.1) for .L.str
    gps_app.o(.text.GPS_Task) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_Task) refers to gps_app.o(.text.GPS_Task) for [Anonymous Symbol]
    gps_app.o(.text.parseGpsBuffer) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.parseGpsBuffer) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.parseGpsBuffer) refers to dfltui.o(.text) for __aeabi_ui2d
    gps_app.o(.text.parseGpsBuffer) refers to ddiv.o(.text) for __aeabi_ddiv
    gps_app.o(.text.parseGpsBuffer) refers to dadd.o(.text) for __aeabi_dadd
    gps_app.o(.text.parseGpsBuffer) refers to d2f.o(.text) for __aeabi_d2f
    gps_app.o(.ARM.exidx.text.parseGpsBuffer) refers to gps_app.o(.text.parseGpsBuffer) for [Anonymous Symbol]
    gps_app.o(.text.printGpsBuffer) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.printGpsBuffer) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.printGpsBuffer) refers to gps_app.o(.rodata.str1.1) for .L.str
    gps_app.o(.text.printGpsBuffer) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.printGpsBuffer) refers to gps_app.o(.text.printGpsBuffer) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Test_SimulateData) refers to memseta.o(.text) for __aeabi_memclr
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.rodata.str1.4) for .L__const.GPS_Test_SimulateData.test_nmea
    gps_app.o(.text.GPS_Test_SimulateData) refers to memcpya.o(.text) for __aeabi_memcpy4
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.text.parseGpsBuffer) for parseGpsBuffer
    gps_app.o(.text.GPS_Test_SimulateData) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.rodata.str1.1) for .L.str
    gps_app.o(.text.GPS_Test_SimulateData) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_Test_SimulateData) refers to gps_app.o(.text.GPS_Test_SimulateData) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Virtual_Init) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Virtual_Init) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Virtual_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Virtual_Init) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Virtual_Init) refers to gps_app.o(.rodata.str1.1) for .L.str.1
    gps_app.o(.text.GPS_Virtual_Init) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_Virtual_Init) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.ARM.exidx.text.GPS_Virtual_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.rodata.str1.1) for .L.str.1
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.ARM.exidx.text.GPS_Virtual_SetLocation) refers to gps_app.o(.text.GPS_Virtual_SetLocation) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Virtual_EnableMovement) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Virtual_EnableMovement) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_Virtual_EnableMovement) refers to gps_app.o(.text.GPS_Virtual_EnableMovement) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to gps_app.o(.rodata.str1.1) for .L.str.1
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to printfa.o(i.__0snprintf) for __2snprintf
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to strlen.o(.text) for strlen
    gps_app.o(.text.GPS_Virtual_GenerateData) refers to strncpy.o(.text) for strncpy
    gps_app.o(.ARM.exidx.text.GPS_Virtual_GenerateData) refers to gps_app.o(.text.GPS_Virtual_GenerateData) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_Init) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_Init) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_Init) refers to memseta.o(.text) for __aeabi_memclr4
    navigation_app.o(.text.Navigation_Init) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_Init) refers to navigation_app.o(.rodata.str1.1) for .L.str
    navigation_app.o(.text.Navigation_Init) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_Init) refers to navigation_app.o(.text.Navigation_Init) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    navigation_app.o(.text.Navigation_Task) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_Task) refers to navigation_app.o(.text.Navigation_UpdateProgress) for Navigation_UpdateProgress
    navigation_app.o(.text.Navigation_Task) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_Task) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_Task) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_Task) refers to navigation_app.o(.text.Navigation_Task) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_UpdateProgress) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    navigation_app.o(.text.Navigation_UpdateProgress) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_UpdateProgress) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    navigation_app.o(.text.Navigation_UpdateProgress) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    navigation_app.o(.text.Navigation_UpdateProgress) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    navigation_app.o(.text.Navigation_UpdateProgress) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_UpdateProgress) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    navigation_app.o(.text.Navigation_UpdateProgress) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    navigation_app.o(.text.Navigation_UpdateProgress) refers to navigation_app.o(.rodata.str1.1) for .L.str.24
    navigation_app.o(.text.Navigation_UpdateProgress) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_UpdateProgress) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_UpdateProgress) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_UpdateProgress) refers to navigation_app.o(.text.Navigation_UpdateProgress) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_FindDestination) for Navigation_FindDestination
    navigation_app.o(.text.Navigation_StartNavigation) refers to gps_app.o(.bss..L_MergedGlobals) for g_LatAndLongData
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_StartNavigation) refers to memcpya.o(.text) for __aeabi_memcpy4
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_PlanRoute) for Navigation_PlanRoute
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_StartNavigation) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_StartNavigation) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_StartNavigation) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_StartNavigation) refers to esp01_app.o(.text.esp01_SendLocationData) for esp01_SendLocationData
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.rodata.str1.1) for .L.str.2
    navigation_app.o(.ARM.exidx.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_StartNavigation) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_FindDestination) refers to navigation_app.o(.rodata.destinations) for destinations
    navigation_app.o(.text.Navigation_FindDestination) refers to strcmp.o(.text) for strcmp
    navigation_app.o(.text.Navigation_FindDestination) refers to memcpya.o(.text) for __aeabi_memcpy4
    navigation_app.o(.ARM.exidx.text.Navigation_FindDestination) refers to navigation_app.o(.text.Navigation_FindDestination) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_PlanRoute) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_PlanRoute) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    navigation_app.o(.text.Navigation_PlanRoute) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    navigation_app.o(.text.Navigation_PlanRoute) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    navigation_app.o(.text.Navigation_PlanRoute) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    navigation_app.o(.text.Navigation_PlanRoute) refers to navigation_app.o(.rodata.str1.1) for .L.str.24
    navigation_app.o(.text.Navigation_PlanRoute) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_PlanRoute) refers to printfa.o(i.__0snprintf) for __2snprintf
    navigation_app.o(.ARM.exidx.text.Navigation_PlanRoute) refers to navigation_app.o(.text.Navigation_PlanRoute) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_StopNavigation) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.rodata.str1.1) for .L.str.6
    navigation_app.o(.text.Navigation_StopNavigation) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_StopNavigation) refers to navigation_app.o(.text.Navigation_StopNavigation) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_ProcessCommand) refers to strncmp.o(.text) for strncmp
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.rodata.destinations) for destinations
    navigation_app.o(.text.Navigation_ProcessCommand) refers to strcmp.o(.text) for strcmp
    navigation_app.o(.text.Navigation_ProcessCommand) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_ProcessCommand) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_StartNavigation) for Navigation_StartNavigation
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.rodata.str1.1) for .L.str.6
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_PrintDestinations) for Navigation_PrintDestinations
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_PrintStatus) for Navigation_PrintStatus
    navigation_app.o(.ARM.exidx.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_ProcessCommand) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_PrintDestinations) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_PrintDestinations) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_PrintDestinations) refers to navigation_app.o(.rodata.destinations) for destinations
    navigation_app.o(.text.Navigation_PrintDestinations) refers to navigation_app.o(.rodata.str1.1) for .L.str.36
    navigation_app.o(.ARM.exidx.text.Navigation_PrintDestinations) refers to navigation_app.o(.text.Navigation_PrintDestinations) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_PrintStatus) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_PrintStatus) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_PrintStatus) refers to navigation_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    navigation_app.o(.text.Navigation_PrintStatus) refers to navigation_app.o(.rodata.str1.1) for .L.str.34
    navigation_app.o(.text.Navigation_PrintStatus) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_PrintStatus) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.ARM.exidx.text.Navigation_PrintStatus) refers to navigation_app.o(.text.Navigation_PrintStatus) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_CalculateDistance) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    navigation_app.o(.text.Navigation_CalculateDistance) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    navigation_app.o(.text.Navigation_CalculateDistance) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    navigation_app.o(.ARM.exidx.text.Navigation_CalculateDistance) refers to navigation_app.o(.text.Navigation_CalculateDistance) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_CalculateBearing) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    navigation_app.o(.text.Navigation_CalculateBearing) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    navigation_app.o(.text.Navigation_CalculateBearing) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    navigation_app.o(.text.Navigation_CalculateBearing) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    navigation_app.o(.ARM.exidx.text.Navigation_CalculateBearing) refers to navigation_app.o(.text.Navigation_CalculateBearing) for [Anonymous Symbol]
    uart6_app.o(.text.Uart6_Init) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart6_app.o(.text.Uart6_Init) refers to uart_driver.o(.bss.ring_buffer_input) for ring_buffer_input
    uart6_app.o(.text.Uart6_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.bss.huart6) for huart6
    uart6_app.o(.text.Uart6_Init) refers to uart6_driver.o(.bss.uart6_rx_dma_buffer) for uart6_rx_dma_buffer
    uart6_app.o(.text.Uart6_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    uart6_app.o(.ARM.exidx.text.Uart6_Init) refers to uart6_app.o(.text.Uart6_Init) for [Anonymous Symbol]
    uart6_app.o(.text.Uart6_Task) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart6_app.o(.text.Uart6_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart6_app.o(.text.Uart6_Task) refers to uart6_driver.o(.bss.uart6_data_buffer) for uart6_data_buffer
    uart6_app.o(.text.Uart6_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart6_app.o(.text.Uart6_Task) refers to memcmp.o(.text) for memcmp
    uart6_app.o(.text.Uart6_Task) refers to usart.o(.bss.huart6) for huart6
    uart6_app.o(.text.Uart6_Task) refers to usart.o(.text.my_printf) for my_printf
    uart6_app.o(.text.Uart6_Task) refers to usart.o(.bss.huart1) for huart1
    uart6_app.o(.text.Uart6_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart6_app.o(.ARM.exidx.text.Uart6_Task) refers to uart6_app.o(.text.Uart6_Task) for [Anonymous Symbol]
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    fmodf.o(i.__hardfp_fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem.o(.text) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to errno.o(i.__set_errno) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frem.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frem.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (1536 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C2_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (62 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (92 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM4_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Encoder_MspDeInit), (96 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART4_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART3_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART6_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (226 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (376 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (272 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (518 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (580 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (290 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (326 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (462 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (256 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (294 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (354 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (594 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (130 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (552 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (436 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (454 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (578 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (644 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (628 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (212 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (222 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (282 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (154 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (384 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.text.Encoder_Driver_Init), (32 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init), (8 bytes).
    Removing encoder_driver.o(.text.Encoder_Driver_Update), (84 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.text.IIC_ReadByte), (40 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.text.Ping), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Digtal), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (50 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Anolog_Normalize), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Offset), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset), (8 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_status), (20 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_put_force), (208 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_peek), (132 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar), (100 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_getchar), (116 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_reset), (6 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset), (8 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.text.Uart_Printf), (62 bytes).
    Removing uart_driver.o(.ARM.exidx.text.Uart_Printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing uart2_driver.o(.text), (0 bytes).
    Removing uart2_driver.o(.ARM.exidx.text.Uart2_Printf), (8 bytes).
    Removing uart2_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART2), (8 bytes).
    Removing uart3_driver.o(.text), (0 bytes).
    Removing uart3_driver.o(.text.Uart3_Printf), (62 bytes).
    Removing uart3_driver.o(.ARM.exidx.text.Uart3_Printf), (8 bytes).
    Removing uart3_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART3), (8 bytes).
    Removing uart3_driver.o(.bss.uart3_ring_buffer_input), (128 bytes).
    Removing uart6_driver.o(.text), (0 bytes).
    Removing uart6_driver.o(.text.Uart6_Printf), (62 bytes).
    Removing uart6_driver.o(.ARM.exidx.text.Uart6_Printf), (8 bytes).
    Removing uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6), (100 bytes).
    Removing uart6_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART6), (8 bytes).
    Removing uart6_driver.o(.bss.uart6_ring_buffer), (12 bytes).
    Removing uart6_driver.o(.bss.uart6_rx_dma_buffer), (512 bytes).
    Removing uart6_driver.o(.bss.uart6_ring_buffer_input), (512 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.text.System_Init), (22 bytes).
    Removing scheduler.o(.ARM.exidx.text.System_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Run), (8 bytes).
    Removing uart_app.o(.text), (0 bytes).
    Removing uart_app.o(.ARM.exidx.text.Uart_Init), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.Uart_Task), (8 bytes).
    Removing uart2_app.o(.text), (0 bytes).
    Removing uart2_app.o(.ARM.exidx.text.Uart2_Init), (8 bytes).
    Removing uart2_app.o(.ARM.exidx.text.Uart2_Task), (8 bytes).
    Removing uart3_app.o(.text), (0 bytes).
    Removing uart3_app.o(.text.Uart3_Init), (14 bytes).
    Removing uart3_app.o(.ARM.exidx.text.Uart3_Init), (8 bytes).
    Removing uart3_app.o(.ARM.exidx.text.Uart3_Task), (8 bytes).
    Removing esp01_app.o(.text), (0 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Init), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Task), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_InitSequence), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_UploadGPSData), (8 bytes).
    Removing esp01_app.o(.text.esp01_CheckConnection), (46 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_CheckConnection), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_GetState), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetConnected), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_EstablishTCPConnection), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetTCPConnected), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetDataSendReady), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_ResetTCPState), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Reset), (8 bytes).
    Removing esp01_app.o(.text.esp01_SendCommand), (72 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendCommand), (8 bytes).
    Removing esp01_app.o(.text.esp01_CheckTCPStatus), (62 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_CheckTCPStatus), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_TryTCPWithIP), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_GetRealLocation), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendDataWithRecovery), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendLocationData), (8 bytes).
    Removing esp01_app.o(.text.esp01_SendNavigationData), (884 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendNavigationData), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_CheckAndRecoverTCPConnection), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostics), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_ForceReset), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_StartInit), (8 bytes).
    Removing gps_app.o(.text), (0 bytes).
    Removing gps_app.o(.text.GPS_Init), (80 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Init), (8 bytes).
    Removing gps_app.o(.text.clrStruct), (18 bytes).
    Removing gps_app.o(.ARM.exidx.text.clrStruct), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Task), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.parseGpsBuffer), (8 bytes).
    Removing gps_app.o(.text.printGpsBuffer), (64 bytes).
    Removing gps_app.o(.ARM.exidx.text.printGpsBuffer), (8 bytes).
    Removing gps_app.o(.text.GPS_Test_SimulateData), (100 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Test_SimulateData), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_Init), (8 bytes).
    Removing gps_app.o(.text.GPS_Virtual_SetLocation), (308 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_SetLocation), (8 bytes).
    Removing gps_app.o(.text.GPS_Virtual_EnableMovement), (72 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_EnableMovement), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_GenerateData), (8 bytes).
    Removing gps_app.o(.rodata.str1.4), (74 bytes).
    Removing navigation_app.o(.text), (0 bytes).
    Removing navigation_app.o(.text.Navigation_Init), (54 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_Init), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_Task), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_UpdateProgress), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_StartNavigation), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_FindDestination), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PlanRoute), (8 bytes).
    Removing navigation_app.o(.text.Navigation_StopNavigation), (46 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_StopNavigation), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_ProcessCommand), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PrintDestinations), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PrintStatus), (8 bytes).
    Removing navigation_app.o(.text.Navigation_CalculateDistance), (204 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_CalculateDistance), (8 bytes).
    Removing navigation_app.o(.text.Navigation_CalculateBearing), (188 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_CalculateBearing), (8 bytes).
    Removing uart6_app.o(.text), (0 bytes).
    Removing uart6_app.o(.text.Uart6_Init), (68 bytes).
    Removing uart6_app.o(.ARM.exidx.text.Uart6_Init), (8 bytes).
    Removing uart6_app.o(.ARM.exidx.text.Uart6_Task), (8 bytes).

1012 unused section(s) (total 63162 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprem.c                0x00000000   Number         0  frem.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    GPS_app.c                                0x00000000   Number         0  gps_app.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    esp01_app.c                              0x00000000   Number         0  esp01_app.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    navigation_app.c                         0x00000000   Number         0  navigation_app.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    uart2_app.c                              0x00000000   Number         0  uart2_app.o ABSOLUTE
    uart2_driver.c                           0x00000000   Number         0  uart2_driver.o ABSOLUTE
    uart3_app.c                              0x00000000   Number         0  uart3_app.o ABSOLUTE
    uart3_driver.c                           0x00000000   Number         0  uart3_driver.o ABSOLUTE
    uart6_app.c                              0x00000000   Number         0  uart6_app.o ABSOLUTE
    uart6_driver.c                           0x00000000   Number         0  uart6_driver.o ABSOLUTE
    uart_app.c                               0x00000000   Number         0  uart_app.o ABSOLUTE
    uart_driver.c                            0x00000000   Number         0  uart_driver.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memcpya.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  strstr.o(.text)
    .text                                    0x0800028e   Section        0  strncpy.o(.text)
    .text                                    0x080002a6   Section        0  strchr.o(.text)
    .text                                    0x080002ba   Section        0  strlen.o(.text)
    .text                                    0x080002c8   Section        0  strcmp.o(.text)
    .text                                    0x080002e4   Section        0  memcmp.o(.text)
    .text                                    0x080002fe   Section        0  strncmp.o(.text)
    .text                                    0x0800031c   Section        0  dadd.o(.text)
    .text                                    0x0800046a   Section        0  ddiv.o(.text)
    .text                                    0x08000548   Section        0  dfltui.o(.text)
    .text                                    0x08000562   Section        0  f2d.o(.text)
    .text                                    0x08000588   Section        0  d2f.o(.text)
    .text                                    0x080005c0   Section        0  uidiv.o(.text)
    .text                                    0x080005ec   Section        0  llshl.o(.text)
    .text                                    0x0800060a   Section        0  llushr.o(.text)
    .text                                    0x0800062a   Section        0  llsshr.o(.text)
    .text                                    0x0800064e   Section        0  iusefp.o(.text)
    .text                                    0x0800064e   Section        0  fepilogue.o(.text)
    .text                                    0x080006bc   Section        0  frem.o(.text)
    .text                                    0x0800070e   Section        0  depilogue.o(.text)
    .text                                    0x080007c8   Section        0  dmul.o(.text)
    .text                                    0x080008ac   Section        0  dfixul.o(.text)
    .text                                    0x080008dc   Section       48  cdrcmple.o(.text)
    .text                                    0x0800090c   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x0800093c   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000940   Section        0  stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler)
    [Anonymous Symbol]                       0x0800094c   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x08000958   Section        0  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    [Anonymous Symbol]                       0x08000964   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08000970   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000974   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x0800097c   Section        0  gps_app.o(.text.GPS_Task)
    [Anonymous Symbol]                       0x08000b60   Section        0  gps_app.o(.text.GPS_Virtual_GenerateData)
    [Anonymous Symbol]                       0x08000e10   Section        0  gps_app.o(.text.GPS_Virtual_Init)
    [Anonymous Symbol]                       0x08000eb0   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08000f40   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08000f64   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08001128   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x0800128c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x08001330   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08001358   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080014f8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    [Anonymous Symbol]                       0x08001508   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08001514   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001520   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x08001684   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x0800170c   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08001728   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001760   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x080017a8   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x080017e0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001804   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x0800185c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x0800187c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x080019e0   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08001a08   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001a30   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001a9c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001e48   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001e74   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x08001ec0   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08001f7c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08001fd8   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x0800200c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x080021ac   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    [Anonymous Symbol]                       0x08002260   Section        0  tim.o(.text.HAL_TIM_Encoder_MspInit)
    [Anonymous Symbol]                       0x08002320   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x08002540   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x0800259c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x080025a0   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x08002790   Section        0  uart_driver.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08002824   Section        0  uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2)
    [Anonymous Symbol]                       0x08002888   Section        0  uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3)
    [Anonymous Symbol]                       0x080028e8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    [Anonymous Symbol]                       0x08002b04   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08002b08   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x0800309c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x080030fc   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08003444   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08003448   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x0800344c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x080035e0   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x080035e4   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080035e8   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08003664   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x080037b8   Section        0  i2c.o(.text.MX_I2C2_Init)
    [Anonymous Symbol]                       0x080037fc   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08003964   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x080039cc   Section        0  tim.o(.text.MX_TIM4_Init)
    [Anonymous Symbol]                       0x08003a34   Section        0  usart.o(.text.MX_UART4_Init)
    [Anonymous Symbol]                       0x08003a70   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08003aac   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08003ae8   Section        0  usart.o(.text.MX_USART3_UART_Init)
    [Anonymous Symbol]                       0x08003b24   Section        0  usart.o(.text.MX_USART6_UART_Init)
    [Anonymous Symbol]                       0x08003b60   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08003b64   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08003b68   Section        0  navigation_app.o(.text.Navigation_FindDestination)
    [Anonymous Symbol]                       0x08003c98   Section        0  navigation_app.o(.text.Navigation_PlanRoute)
    [Anonymous Symbol]                       0x08003ff4   Section        0  navigation_app.o(.text.Navigation_PrintDestinations)
    [Anonymous Symbol]                       0x080040d4   Section        0  navigation_app.o(.text.Navigation_PrintStatus)
    [Anonymous Symbol]                       0x080041fc   Section        0  navigation_app.o(.text.Navigation_ProcessCommand)
    [Anonymous Symbol]                       0x08004468   Section        0  navigation_app.o(.text.Navigation_StartNavigation)
    [Anonymous Symbol]                       0x080045d0   Section        0  navigation_app.o(.text.Navigation_Task)
    [Anonymous Symbol]                       0x08004640   Section        0  navigation_app.o(.text.Navigation_UpdateProgress)
    [Anonymous Symbol]                       0x08004968   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x0800496c   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08004970   Section        0  scheduler.o(.text.Scheduler_Init)
    [Anonymous Symbol]                       0x08004990   Section        0  scheduler.o(.text.Scheduler_Run)
    [Anonymous Symbol]                       0x080049dc   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080049e0   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08004a88   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08004a9c   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x08004bd8   Section        0  stm32f4xx_it.o(.text.UART4_IRQHandler)
    UART_DMAAbortOnError                     0x08004be5   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08004be4   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x08004bf1   Thumb Code   380  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08004bf0   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x08004d6d   Thumb Code   350  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x08004d6c   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08004ecd   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08004ecc   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x08004ee5   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08004ee4   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08004fe5   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08004fe4   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x080050cc   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x080050d8   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x080050e4   Section        0  stm32f4xx_it.o(.text.USART3_IRQHandler)
    [Anonymous Symbol]                       0x080050f0   Section        0  stm32f4xx_it.o(.text.USART6_IRQHandler)
    [Anonymous Symbol]                       0x080050fc   Section        0  uart2_app.o(.text.Uart2_Init)
    [Anonymous Symbol]                       0x08005144   Section        0  uart2_driver.o(.text.Uart2_Printf)
    [Anonymous Symbol]                       0x080051b0   Section        0  uart2_app.o(.text.Uart2_Task)
    [Anonymous Symbol]                       0x08005690   Section        0  uart3_app.o(.text.Uart3_Task)
    [Anonymous Symbol]                       0x080056a0   Section        0  uart6_app.o(.text.Uart6_Task)
    [Anonymous Symbol]                       0x08005734   Section        0  uart_app.o(.text.Uart_Init)
    [Anonymous Symbol]                       0x080057c8   Section        0  uart_app.o(.text.Uart_Task)
    [Anonymous Symbol]                       0x08005e24   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08005e28   Section        0  esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection)
    [Anonymous Symbol]                       0x08005fcc   Section        0  esp01_app.o(.text.esp01_EstablishTCPConnection)
    [Anonymous Symbol]                       0x080062a8   Section        0  esp01_app.o(.text.esp01_ForceReset)
    [Anonymous Symbol]                       0x080063a4   Section        0  esp01_app.o(.text.esp01_GetRealLocation)
    [Anonymous Symbol]                       0x08006450   Section        0  esp01_app.o(.text.esp01_GetState)
    [Anonymous Symbol]                       0x0800645c   Section        0  esp01_app.o(.text.esp01_Init)
    [Anonymous Symbol]                       0x08006554   Section        0  esp01_app.o(.text.esp01_InitSequence)
    [Anonymous Symbol]                       0x080066e4   Section        0  esp01_app.o(.text.esp01_NetworkDiagnostics)
    [Anonymous Symbol]                       0x08006820   Section        0  esp01_app.o(.text.esp01_Reset)
    [Anonymous Symbol]                       0x0800686c   Section        0  esp01_app.o(.text.esp01_ResetTCPState)
    [Anonymous Symbol]                       0x08006880   Section        0  esp01_app.o(.text.esp01_SendDataWithRecovery)
    [Anonymous Symbol]                       0x08006a34   Section        0  esp01_app.o(.text.esp01_SendLocationData)
    [Anonymous Symbol]                       0x08006a38   Section        0  esp01_app.o(.text.esp01_SetConnected)
    [Anonymous Symbol]                       0x08006ae4   Section        0  esp01_app.o(.text.esp01_SetDataSendReady)
    [Anonymous Symbol]                       0x08006b1c   Section        0  esp01_app.o(.text.esp01_SetTCPConnected)
    [Anonymous Symbol]                       0x08006b64   Section        0  esp01_app.o(.text.esp01_StartInit)
    [Anonymous Symbol]                       0x08006bc4   Section        0  esp01_app.o(.text.esp01_Task)
    [Anonymous Symbol]                       0x08006d08   Section        0  esp01_app.o(.text.esp01_TryTCPWithIP)
    [Anonymous Symbol]                       0x08006e70   Section        0  esp01_app.o(.text.esp01_UploadGPSData)
    [Anonymous Symbol]                       0x0800710c   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x0800714c   Section        0  usart.o(.text.my_printf)
    [Anonymous Symbol]                       0x08007190   Section        0  gps_app.o(.text.parseGpsBuffer)
    [Anonymous Symbol]                       0x080076b8   Section        0  ringbuffer.o(.text.rt_ringbuffer_data_len)
    [Anonymous Symbol]                       0x080076fc   Section        0  ringbuffer.o(.text.rt_ringbuffer_get)
    [Anonymous Symbol]                       0x080077b4   Section        0  ringbuffer.o(.text.rt_ringbuffer_init)
    [Anonymous Symbol]                       0x080077c4   Section        0  ringbuffer.o(.text.rt_ringbuffer_put)
    i.__0snprintf                            0x08007878   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x080078ac   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x080078e0   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan2f                        0x08007908   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_cosf                          0x08007bb4   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_fmodf                         0x08007d04   Section        0  fmodf.o(i.__hardfp_fmodf)
    i.__hardfp_sinf                          0x08007db4   Section        0  sinf.o(i.__hardfp_sinf)
    i.__mathlib_flt_infnan                   0x08007f44   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x08007f4a   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x08007f50   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08007f60   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08007f70   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x080080c4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080080d2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080080d4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x080080e4   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x080080f1   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x080080f0   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x08008275   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08008274   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x08008951   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x08008950   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x08008975   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x08008974   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x080089a3   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x080089a2   Section        0  printfa.o(i._snputc)
    twooverpi                                0x080089b8   Data          32  rredf.o(.constdata)
    .constdata                               0x080089b8   Section       32  rredf.o(.constdata)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x080089f0   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x080089f0   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    destinations                             0x080089f8   Data        2080  navigation_app.o(.rodata.destinations)
    [Anonymous Symbol]                       0x080089f8   Section        0  navigation_app.o(.rodata.destinations)
    .L.str.11                                0x08009218   Data          47  uart_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08009218   Section        0  uart_app.o(.rodata.str1.1)
    .L.str.13                                0x08009247   Data          25  uart_app.o(.rodata.str1.1)
    .L.str.22                                0x08009260   Data          22  uart_app.o(.rodata.str1.1)
    .L.str.31                                0x08009276   Data          34  uart_app.o(.rodata.str1.1)
    .L.str.33                                0x08009298   Data          28  uart_app.o(.rodata.str1.1)
    .L.str.41                                0x080092b4   Data          37  uart_app.o(.rodata.str1.1)
    .L.str.29                                0x080092d9   Data          39  uart_app.o(.rodata.str1.1)
    .L.str.37                                0x08009300   Data          44  uart_app.o(.rodata.str1.1)
    .L.str.10                                0x0800932c   Data          50  uart_app.o(.rodata.str1.1)
    .L.str.26                                0x0800935e   Data          10  uart_app.o(.rodata.str1.1)
    .L.str.44                                0x08009368   Data          76  uart_app.o(.rodata.str1.1)
    .L.str.47                                0x080093b4   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.51                                0x080093c7   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.46                                0x080093db   Data          28  uart_app.o(.rodata.str1.1)
    .L.str.6                                 0x080093f7   Data          59  uart_app.o(.rodata.str1.1)
    .L.str.56                                0x08009432   Data          27  uart_app.o(.rodata.str1.1)
    .L.str.18                                0x0800944d   Data          27  uart_app.o(.rodata.str1.1)
    .L.str.15                                0x08009468   Data          22  uart_app.o(.rodata.str1.1)
    .L.str.8                                 0x0800947e   Data          40  uart_app.o(.rodata.str1.1)
    .L.str.17                                0x080094a6   Data          28  uart_app.o(.rodata.str1.1)
    .L.str.16                                0x080094c2   Data          30  uart_app.o(.rodata.str1.1)
    .L.str.20                                0x080094e0   Data          25  uart_app.o(.rodata.str1.1)
    .L.str.19                                0x080094f9   Data          23  uart_app.o(.rodata.str1.1)
    .L.str.50                                0x08009510   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.53                                0x08009524   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.52                                0x08009538   Data          18  uart_app.o(.rodata.str1.1)
    .L.str.54                                0x0800954a   Data          18  uart_app.o(.rodata.str1.1)
    .L.str.35                                0x0800955c   Data          33  uart_app.o(.rodata.str1.1)
    .L.str.39                                0x0800957d   Data          32  uart_app.o(.rodata.str1.1)
    .L.str.49                                0x0800959d   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.48                                0x080095b1   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.34                                0x080095c4   Data          37  uart_app.o(.rodata.str1.1)
    .L.str.38                                0x080095e9   Data          36  uart_app.o(.rodata.str1.1)
    .L.str.43                                0x0800960d   Data          36  uart_app.o(.rodata.str1.1)
    .L.str.55                                0x08009631   Data          17  uart_app.o(.rodata.str1.1)
    .L.str.9                                 0x08009642   Data          53  uart_app.o(.rodata.str1.1)
    .L.str.7                                 0x08009677   Data          38  uart_app.o(.rodata.str1.1)
    .L.str.5                                 0x0800969d   Data           7  uart_app.o(.rodata.str1.1)
    .L.str.28                                0x080096a4   Data           9  uart_app.o(.rodata.str1.1)
    .L.str.40                                0x080096ad   Data          14  uart_app.o(.rodata.str1.1)
    .L.str.42                                0x080096bb   Data          13  uart_app.o(.rodata.str1.1)
    .L.str.14                                0x080096c8   Data          11  uart_app.o(.rodata.str1.1)
    .L.str.45                                0x080096d3   Data          11  uart_app.o(.rodata.str1.1)
    .L.str.23                                0x080096de   Data          13  uart_app.o(.rodata.str1.1)
    .L.str.30                                0x080096eb   Data          16  uart_app.o(.rodata.str1.1)
    .L.str.21                                0x080096fb   Data          10  uart_app.o(.rodata.str1.1)
    .L.str.27                                0x08009705   Data          10  uart_app.o(.rodata.str1.1)
    .L.str.32                                0x0800970f   Data          13  uart_app.o(.rodata.str1.1)
    .L.str.36                                0x0800971c   Data          12  uart_app.o(.rodata.str1.1)
    .L.str.25                                0x08009728   Data           9  uart_app.o(.rodata.str1.1)
    .L.str.11                                0x08009731   Data          55  uart2_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08009731   Section        0  uart2_app.o(.rodata.str1.1)
    .L.str.19                                0x08009768   Data          54  uart2_app.o(.rodata.str1.1)
    .L.str.43                                0x0800979e   Data          41  uart2_app.o(.rodata.str1.1)
    .L.str.16                                0x080097c7   Data          42  uart2_app.o(.rodata.str1.1)
    .L.str.37                                0x080097f1   Data          39  uart2_app.o(.rodata.str1.1)
    .L.str.40                                0x08009818   Data          31  uart2_app.o(.rodata.str1.1)
    .L.str.35                                0x08009837   Data          42  uart2_app.o(.rodata.str1.1)
    .L.str.23                                0x08009861   Data          33  uart2_app.o(.rodata.str1.1)
    .L.str.33                                0x08009882   Data          43  uart2_app.o(.rodata.str1.1)
    .L.str.14                                0x080098ad   Data          44  uart2_app.o(.rodata.str1.1)
    .L.str.24                                0x080098d9   Data          39  uart2_app.o(.rodata.str1.1)
    .L.str.44                                0x08009900   Data          41  uart2_app.o(.rodata.str1.1)
    .L.str.26                                0x08009929   Data          46  uart2_app.o(.rodata.str1.1)
    .L.str.30                                0x08009957   Data          57  uart2_app.o(.rodata.str1.1)
    .L.str.28                                0x08009990   Data          52  uart2_app.o(.rodata.str1.1)
    .L.str.21                                0x080099c4   Data          44  uart2_app.o(.rodata.str1.1)
    .L.str.6                                 0x080099f0   Data          53  uart2_app.o(.rodata.str1.1)
    .L.str.17                                0x08009a25   Data          38  uart2_app.o(.rodata.str1.1)
    .L.str.38                                0x08009a4b   Data          28  uart2_app.o(.rodata.str1.1)
    .L.str.9                                 0x08009a67   Data          43  uart2_app.o(.rodata.str1.1)
    .L.str.41                                0x08009a92   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.15                                0x08009a9b   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.13                                0x08009aa4   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.25                                0x08009aad   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.12                                0x08009ab9   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.22                                0x08009ac1   Data           7  uart2_app.o(.rodata.str1.1)
    .L.str.8                                 0x08009ac8   Data          18  uart2_app.o(.rodata.str1.1)
    .L.str.36                                0x08009ada   Data          11  uart2_app.o(.rodata.str1.1)
    .L.str.32                                0x08009ae5   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.42                                0x08009af1   Data           7  uart2_app.o(.rodata.str1.1)
    .L.str.20                                0x08009af8   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.29                                0x08009b00   Data          13  uart2_app.o(.rodata.str1.1)
    .L.str.39                                0x08009b08   Data           5  uart2_app.o(.rodata.str1.1)
    .L.str.31                                0x08009b0d   Data           6  uart2_app.o(.rodata.str1.1)
    .L.str.10                                0x08009b13   Data          11  uart2_app.o(.rodata.str1.1)
    .L.str.7                                 0x08009b16   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.34                                0x08009b1e   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.27                                0x08009b2a   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.5                                 0x08009b33   Data           6  uart2_app.o(.rodata.str1.1)
    .L.str.68                                0x08009b39   Data          33  esp01_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08009b39   Section        0  esp01_app.o(.rodata.str1.1)
    .L.str.59                                0x08009b5a   Data          54  esp01_app.o(.rodata.str1.1)
    .L.str.35                                0x08009b90   Data          40  esp01_app.o(.rodata.str1.1)
    .L.str.62                                0x08009bb8   Data          34  esp01_app.o(.rodata.str1.1)
    .L.str.49                                0x08009c03   Data          44  esp01_app.o(.rodata.str1.1)
    .L.str.51                                0x08009c2f   Data          48  esp01_app.o(.rodata.str1.1)
    .L.str.41                                0x08009c88   Data          47  esp01_app.o(.rodata.str1.1)
    .L.str.89                                0x08009cb7   Data          32  esp01_app.o(.rodata.str1.1)
    .L.str.45                                0x08009cd7   Data          43  esp01_app.o(.rodata.str1.1)
    .L.str.12                                0x08009d02   Data          37  esp01_app.o(.rodata.str1.1)
    .L.str.47                                0x08009d27   Data          55  esp01_app.o(.rodata.str1.1)
    .L.str.39                                0x08009d5e   Data          62  esp01_app.o(.rodata.str1.1)
    .L.str.10                                0x08009d9c   Data          41  esp01_app.o(.rodata.str1.1)
    .L.str.103                               0x08009dc5   Data          33  esp01_app.o(.rodata.str1.1)
    .L.str.43                                0x08009de6   Data          43  esp01_app.o(.rodata.str1.1)
    .L.str.104                               0x08009e11   Data          28  esp01_app.o(.rodata.str1.1)
    .L.str.50                                0x08009e2d   Data          56  esp01_app.o(.rodata.str1.1)
    .L.str.29                                0x08009e65   Data          41  esp01_app.o(.rodata.str1.1)
    .L.str.91                                0x08009e8e   Data          40  esp01_app.o(.rodata.str1.1)
    .L.str.88                                0x08009eb6   Data          51  esp01_app.o(.rodata.str1.1)
    .L.str.67                                0x08009ee9   Data          83  esp01_app.o(.rodata.str1.1)
    .L.str.33                                0x08009f3c   Data          28  esp01_app.o(.rodata.str1.1)
    .L.str.102                               0x08009f87   Data          52  esp01_app.o(.rodata.str1.1)
    .L.str                                   0x08009fbb   Data          50  esp01_app.o(.rodata.str1.1)
    .L.str.19                                0x08009fed   Data          12  esp01_app.o(.rodata.str1.1)
    .L.str.44                                0x08009ff9   Data          14  esp01_app.o(.rodata.str1.1)
    .L.str.55                                0x0800a007   Data          36  esp01_app.o(.rodata.str1.1)
    .L.str.66                                0x0800a076   Data          36  esp01_app.o(.rodata.str1.1)
    .L.str.65                                0x0800a09a   Data          62  esp01_app.o(.rodata.str1.1)
    .L.str.105                               0x0800a0d8   Data          11  esp01_app.o(.rodata.str1.1)
    .L.str.30                                0x0800a0e3   Data          15  esp01_app.o(.rodata.str1.1)
    .L.str.13                                0x0800a0f2   Data           9  esp01_app.o(.rodata.str1.1)
    .L.str.90                                0x0800a0fb   Data          45  esp01_app.o(.rodata.str1.1)
    .L.str.72                                0x0800a11a   Data          76  esp01_app.o(.rodata.str1.1)
    .L.str.52                                0x0800a128   Data          40  esp01_app.o(.rodata.str1.1)
    .L.str.94                                0x0800a150   Data          47  esp01_app.o(.rodata.str1.1)
    .L.str.73                                0x0800a166   Data          47  esp01_app.o(.rodata.str1.1)
    .L.str.74                                0x0800a195   Data          38  esp01_app.o(.rodata.str1.1)
    .L.str.54                                0x0800a1ab   Data          43  esp01_app.o(.rodata.str1.1)
    .L.str.75                                0x0800a1bb   Data          37  esp01_app.o(.rodata.str1.1)
    .L.str.40                                0x0800a1d6   Data          58  esp01_app.o(.rodata.str1.1)
    .L.str.76                                0x0800a1e0   Data          29  esp01_app.o(.rodata.str1.1)
    .L.str.77                                0x0800a1fd   Data          29  esp01_app.o(.rodata.str1.1)
    .L.str.1                                 0x0800a210   Data          19  esp01_app.o(.rodata.str1.1)
    .L.str.79                                0x0800a21a   Data          41  esp01_app.o(.rodata.str1.1)
    .L.str.48                                0x0800a223   Data          35  esp01_app.o(.rodata.str1.1)
    .L.str.80                                0x0800a243   Data          44  esp01_app.o(.rodata.str1.1)
    .L.str.63                                0x0800a263   Data          43  esp01_app.o(.rodata.str1.1)
    .L.str.81                                0x0800a26f   Data          41  esp01_app.o(.rodata.str1.1)
    .L.str.82                                0x0800a298   Data          50  esp01_app.o(.rodata.str1.1)
    .L.str.53                                0x0800a2c0   Data          76  esp01_app.o(.rodata.str1.1)
    .L.str.83                                0x0800a2ca   Data          37  esp01_app.o(.rodata.str1.1)
    .L.str.84                                0x0800a2ef   Data          36  esp01_app.o(.rodata.str1.1)
    .L.str.85                                0x0800a313   Data          36  esp01_app.o(.rodata.str1.1)
    .L.str.92                                0x0800a329   Data          54  esp01_app.o(.rodata.str1.1)
    .L.str.86                                0x0800a337   Data          42  esp01_app.o(.rodata.str1.1)
    .L.str.101                               0x0800a35f   Data          44  esp01_app.o(.rodata.str1.1)
    .L.str.20                                0x0800a38b   Data          41  esp01_app.o(.rodata.str1.1)
    .L.str.100                               0x0800a3b4   Data          37  esp01_app.o(.rodata.str1.1)
    .L.str.4                                 0x0800a3d9   Data           8  esp01_app.o(.rodata.str1.1)
    .L.str.32                                0x0800a42d   Data          16  esp01_app.o(.rodata.str1.1)
    .L.str.58                                0x0800a43d   Data          17  esp01_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800a44e   Data          12  esp01_app.o(.rodata.str1.1)
    .L.str.57                                0x0800a45a   Data          55  esp01_app.o(.rodata.str1.1)
    .L.str.42                                0x0800a491   Data          19  esp01_app.o(.rodata.str1.1)
    .L.str.9                                 0x0800a4a4   Data          85  gps_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800a4a4   Section        0  gps_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800a4f9   Data          37  gps_app.o(.rodata.str1.1)
    .L.str                                   0x0800a51e   Data          16  gps_app.o(.rodata.str1.1)
    .L.str.1                                 0x0800a52e   Data          57  gps_app.o(.rodata.str1.1)
    .L.str.7                                 0x0800a567   Data          71  gps_app.o(.rodata.str1.1)
    .L.str                                   0x0800a5ae   Data          83  navigation_app.o(.rodata.str1.1)
    .L.str.9                                 0x0800a5ae   Data          32  navigation_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800a5ae   Section        0  navigation_app.o(.rodata.str1.1)
    .L.str.36                                0x0800a5ce   Data          10  navigation_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800a5d8   Data          27  navigation_app.o(.rodata.str1.1)
    .L.str.27                                0x0800a5f3   Data          81  navigation_app.o(.rodata.str1.1)
    .L.str.34                                0x0800a644   Data          13  navigation_app.o(.rodata.str1.1)
    .L.str.12                                0x0800a651   Data          40  navigation_app.o(.rodata.str1.1)
    .L.str.6                                 0x0800a679   Data          25  navigation_app.o(.rodata.str1.1)
    .L.str.11                                0x0800a692   Data          40  navigation_app.o(.rodata.str1.1)
    .L.str.13                                0x0800a70d   Data          30  navigation_app.o(.rodata.str1.1)
    .L.str.14                                0x0800a72b   Data          47  navigation_app.o(.rodata.str1.1)
    .L.str.10                                0x0800a75a   Data          34  navigation_app.o(.rodata.str1.1)
    .L.str.25                                0x0800a77c   Data          13  navigation_app.o(.rodata.str1.1)
    .L.str.24                                0x0800a782   Data           7  navigation_app.o(.rodata.str1.1)
    _errno                                   0x20000000   Data           4  errno.o(.data)
    .data                                    0x20000000   Section        4  errno.o(.data)
    .L_MergedGlobals                         0x20000004   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000004   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    scheduler_task                           0x20000010   Data          84  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20000010   Section        0  scheduler.o(.data.scheduler_task)
    .L_MergedGlobals                         0x20000068   Data          16  esp01_app.o(.bss..L_MergedGlobals)
    esp01_state                              0x20000068   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000068   Section        0  esp01_app.o(.bss..L_MergedGlobals)
    esp01_retry_count                        0x20000069   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    esp01_init_step                          0x2000006a   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    tcp_connected                            0x2000006b   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    data_send_ready                          0x2000006c   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    persistent_connection                    0x2000006d   Data           1  esp01_app.o(.bss..L_MergedGlobals)
    esp01_last_cmd_time                      0x20000070   Data           4  esp01_app.o(.bss..L_MergedGlobals)
    esp01_Task.last_upload_time              0x20000074   Data           4  esp01_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000078   Data          44  gps_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000078   Section        0  gps_app.o(.bss..L_MergedGlobals)
    virtual_gps.6                            0x2000007c   Data           1  gps_app.o(.bss..L_MergedGlobals)
    virtual_gps.0                            0x20000088   Data           4  gps_app.o(.bss..L_MergedGlobals)
    virtual_gps.1                            0x2000008c   Data           4  gps_app.o(.bss..L_MergedGlobals)
    virtual_gps.2                            0x20000090   Data           4  gps_app.o(.bss..L_MergedGlobals)
    virtual_gps.5                            0x20000094   Data           4  gps_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x200000a4   Data          12  navigation_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200000a4   Section        0  navigation_app.o(.bss..L_MergedGlobals)
    Navigation_Task.last_update              0x200000a8   Data           4  navigation_app.o(.bss..L_MergedGlobals)
    Navigation_UpdateProgress.last_instruction 0x200000ac   Data           4  navigation_app.o(.bss..L_MergedGlobals)
    Uart2_Task.last_receive_time             0x2000026c   Data           4  uart2_app.o(.bss.Uart2_Task.last_receive_time)
    [Anonymous Symbol]                       0x2000026c   Section        0  uart2_app.o(.bss.Uart2_Task.last_receive_time)
    Uart_Init.init_done                      0x20000270   Data           1  uart_app.o(.bss.Uart_Init.init_done)
    [Anonymous Symbol]                       0x20000270   Section        0  uart_app.o(.bss.Uart_Init.init_done)
    Uart_Task.last_heartbeat                 0x20000274   Data           4  uart_app.o(.bss.Uart_Task.last_heartbeat)
    [Anonymous Symbol]                       0x20000274   Section        0  uart_app.o(.bss.Uart_Task.last_heartbeat)
    http_request_buffer                      0x200021bc   Data         400  esp01_app.o(.bss.http_request_buffer)
    [Anonymous Symbol]                       0x200021bc   Section        0  esp01_app.o(.bss.http_request_buffer)
    url_params_buffer                        0x20002f5c   Data         300  esp01_app.o(.bss.url_params_buffer)
    [Anonymous Symbol]                       0x20002f5c   Section        0  esp01_app.o(.bss.url_params_buffer)
    STACK                                    0x20003090   Section     4096  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000223   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800026b   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x0800028f   Thumb Code    24  strncpy.o(.text)
    strchr                                   0x080002a7   Thumb Code    20  strchr.o(.text)
    strlen                                   0x080002bb   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002c9   Thumb Code    28  strcmp.o(.text)
    memcmp                                   0x080002e5   Thumb Code    26  memcmp.o(.text)
    strncmp                                  0x080002ff   Thumb Code    30  strncmp.o(.text)
    __aeabi_dadd                             0x0800031d   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800045f   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000465   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x0800046b   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x08000549   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2d                              0x08000563   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000589   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080005c1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080005c1   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080005ed   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080005ed   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800060b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800060b   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800062b   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800062b   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800064f   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800064f   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000661   Thumb Code    92  fepilogue.o(.text)
    _frem                                    0x080006bd   Thumb Code    82  frem.o(.text)
    _double_round                            0x0800070f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800072d   Thumb Code   156  depilogue.o(.text)
    __aeabi_dmul                             0x080007c9   Thumb Code   228  dmul.o(.text)
    __aeabi_d2ulz                            0x080008ad   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080008dd   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x0800090d   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x0800090d   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x0800093d   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA1_Stream1_IRQHandler                  0x08000941   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x0800094d   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08000959   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000965   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000971   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x08000975   Thumb Code     6  main.o(.text.Error_Handler)
    GPS_Task                                 0x0800097d   Thumb Code   484  gps_app.o(.text.GPS_Task)
    GPS_Virtual_GenerateData                 0x08000b61   Thumb Code   616  gps_app.o(.text.GPS_Virtual_GenerateData)
    GPS_Virtual_Init                         0x08000e11   Thumb Code   160  gps_app.o(.text.GPS_Virtual_Init)
    HAL_DMA_Abort                            0x08000eb1   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000f41   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000f65   Thumb Code   452  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001129   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x0800128d   Thumb Code   162  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001331   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x08001359   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x080014f9   Thumb Code    16  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08001509   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001515   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_Init                             0x08001521   Thumb Code   356  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08001685   Thumb Code   134  i2c.o(.text.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800170d   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08001729   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001761   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x080017a9   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080017e1   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001805   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800185d   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x0800187d   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080019e1   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001a09   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001a31   Thumb Code   108  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001a9d   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001e49   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08001e75   Thumb Code    76  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08001ec1   Thumb Code   186  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001f7d   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001fd9   Thumb Code    50  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x0800200d   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080021ad   Thumb Code   178  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002261   Thumb Code   192  tim.o(.text.HAL_TIM_Encoder_MspInit)
    HAL_TIM_PWM_ConfigChannel                0x08002321   Thumb Code   544  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002541   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x0800259d   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x080025a1   Thumb Code   494  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002791   Thumb Code   148  uart_driver.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxEventCallback_UART2         0x08002825   Thumb Code   100  uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2)
    HAL_UARTEx_RxEventCallback_UART3         0x08002889   Thumb Code    96  uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3)
    HAL_UART_DMAStop                         0x080028e9   Thumb Code   540  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002b05   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002b09   Thumb Code  1428  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800309d   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x080030fd   Thumb Code   840  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003445   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003449   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800344d   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080035e1   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080035e5   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_DMA_Init                              0x080035e9   Thumb Code   124  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x08003665   Thumb Code   338  gpio.o(.text.MX_GPIO_Init)
    MX_I2C2_Init                             0x080037b9   Thumb Code    66  i2c.o(.text.MX_I2C2_Init)
    MX_TIM1_Init                             0x080037fd   Thumb Code   358  tim.o(.text.MX_TIM1_Init)
    MX_TIM3_Init                             0x08003965   Thumb Code   104  tim.o(.text.MX_TIM3_Init)
    MX_TIM4_Init                             0x080039cd   Thumb Code   104  tim.o(.text.MX_TIM4_Init)
    MX_UART4_Init                            0x08003a35   Thumb Code    60  usart.o(.text.MX_UART4_Init)
    MX_USART1_UART_Init                      0x08003a71   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003aad   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08003ae9   Thumb Code    60  usart.o(.text.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08003b25   Thumb Code    60  usart.o(.text.MX_USART6_UART_Init)
    MemManage_Handler                        0x08003b61   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08003b65   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    Navigation_FindDestination               0x08003b69   Thumb Code   302  navigation_app.o(.text.Navigation_FindDestination)
    Navigation_PlanRoute                     0x08003c99   Thumb Code   720  navigation_app.o(.text.Navigation_PlanRoute)
    Navigation_PrintDestinations             0x08003ff5   Thumb Code   196  navigation_app.o(.text.Navigation_PrintDestinations)
    Navigation_PrintStatus                   0x080040d5   Thumb Code   148  navigation_app.o(.text.Navigation_PrintStatus)
    Navigation_ProcessCommand                0x080041fd   Thumb Code   468  navigation_app.o(.text.Navigation_ProcessCommand)
    Navigation_StartNavigation               0x08004469   Thumb Code   236  navigation_app.o(.text.Navigation_StartNavigation)
    Navigation_Task                          0x080045d1   Thumb Code    80  navigation_app.o(.text.Navigation_Task)
    Navigation_UpdateProgress                0x08004641   Thumb Code   756  navigation_app.o(.text.Navigation_UpdateProgress)
    PendSV_Handler                           0x08004969   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x0800496d   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    Scheduler_Init                           0x08004971   Thumb Code    32  scheduler.o(.text.Scheduler_Init)
    Scheduler_Run                            0x08004991   Thumb Code    74  scheduler.o(.text.Scheduler_Run)
    SysTick_Handler                          0x080049dd   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x080049e1   Thumb Code   168  main.o(.text.SystemClock_Config)
    SystemInit                               0x08004a89   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x08004a9d   Thumb Code   314  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART4_IRQHandler                         0x08004bd9   Thumb Code    12  stm32f4xx_it.o(.text.UART4_IRQHandler)
    USART1_IRQHandler                        0x080050cd   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x080050d9   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    USART3_IRQHandler                        0x080050e5   Thumb Code    12  stm32f4xx_it.o(.text.USART3_IRQHandler)
    USART6_IRQHandler                        0x080050f1   Thumb Code    12  stm32f4xx_it.o(.text.USART6_IRQHandler)
    Uart2_Init                               0x080050fd   Thumb Code    70  uart2_app.o(.text.Uart2_Init)
    Uart2_Printf                             0x08005145   Thumb Code    80  uart2_driver.o(.text.Uart2_Printf)
    Uart2_Task                               0x080051b1   Thumb Code  1104  uart2_app.o(.text.Uart2_Task)
    Uart3_Task                               0x08005691   Thumb Code    14  uart3_app.o(.text.Uart3_Task)
    Uart6_Task                               0x080056a1   Thumb Code   112  uart6_app.o(.text.Uart6_Task)
    Uart_Init                                0x08005735   Thumb Code   108  uart_app.o(.text.Uart_Init)
    Uart_Task                                0x080057c9   Thumb Code  1480  uart_app.o(.text.Uart_Task)
    UsageFault_Handler                       0x08005e25   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    esp01_CheckAndRecoverTCPConnection       0x08005e29   Thumb Code   320  esp01_app.o(.text.esp01_CheckAndRecoverTCPConnection)
    esp01_EstablishTCPConnection             0x08005fcd   Thumb Code   616  esp01_app.o(.text.esp01_EstablishTCPConnection)
    esp01_ForceReset                         0x080062a9   Thumb Code   120  esp01_app.o(.text.esp01_ForceReset)
    esp01_GetRealLocation                    0x080063a5   Thumb Code   172  esp01_app.o(.text.esp01_GetRealLocation)
    esp01_GetState                           0x08006451   Thumb Code    12  esp01_app.o(.text.esp01_GetState)
    esp01_Init                               0x0800645d   Thumb Code   112  esp01_app.o(.text.esp01_Init)
    esp01_InitSequence                       0x08006555   Thumb Code   268  esp01_app.o(.text.esp01_InitSequence)
    esp01_NetworkDiagnostics                 0x080066e5   Thumb Code   180  esp01_app.o(.text.esp01_NetworkDiagnostics)
    esp01_Reset                              0x08006821   Thumb Code    52  esp01_app.o(.text.esp01_Reset)
    esp01_ResetTCPState                      0x0800686d   Thumb Code    18  esp01_app.o(.text.esp01_ResetTCPState)
    esp01_SendDataWithRecovery               0x08006881   Thumb Code   296  esp01_app.o(.text.esp01_SendDataWithRecovery)
    esp01_SendLocationData                   0x08006a35   Thumb Code     4  esp01_app.o(.text.esp01_SendLocationData)
    esp01_SetConnected                       0x08006a39   Thumb Code    80  esp01_app.o(.text.esp01_SetConnected)
    esp01_SetDataSendReady                   0x08006ae5   Thumb Code    28  esp01_app.o(.text.esp01_SetDataSendReady)
    esp01_SetTCPConnected                    0x08006b1d   Thumb Code    28  esp01_app.o(.text.esp01_SetTCPConnected)
    esp01_StartInit                          0x08006b65   Thumb Code    52  esp01_app.o(.text.esp01_StartInit)
    esp01_Task                               0x08006bc5   Thumb Code   212  esp01_app.o(.text.esp01_Task)
    esp01_TryTCPWithIP                       0x08006d09   Thumb Code   232  esp01_app.o(.text.esp01_TryTCPWithIP)
    esp01_UploadGPSData                      0x08006e71   Thumb Code   520  esp01_app.o(.text.esp01_UploadGPSData)
    main                                     0x0800710d   Thumb Code    62  main.o(.text.main)
    my_printf                                0x0800714d   Thumb Code    62  usart.o(.text.my_printf)
    parseGpsBuffer                           0x08007191   Thumb Code  1304  gps_app.o(.text.parseGpsBuffer)
    rt_ringbuffer_data_len                   0x080076b9   Thumb Code    68  ringbuffer.o(.text.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x080076fd   Thumb Code   184  ringbuffer.o(.text.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x080077b5   Thumb Code    14  ringbuffer.o(.text.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080077c5   Thumb Code   178  ringbuffer.o(.text.rt_ringbuffer_put)
    __0snprintf                              0x08007879   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x08007879   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08007879   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08007879   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08007879   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x080078ad   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080078ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080078ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080078ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080078ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x080078e1   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x08007909   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_cosf                            0x08007bb5   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_fmodf                           0x08007d05   Thumb Code   176  fmodf.o(i.__hardfp_fmodf)
    __hardfp_sinf                            0x08007db5   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __mathlib_flt_infnan                     0x08007f45   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x08007f4b   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x08007f51   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08007f61   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08007f71   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x080080c5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080080d3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080080d5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x080080e5   Thumb Code     6  errno.o(i.__set_errno)
    AHBPrescTable                            0x080089d8   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x080089e8   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x0800a78c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a7ac   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    uart_GPS_RX_Buff                         0x20000078   Data           1  gps_app.o(.bss..L_MergedGlobals)
    point1                                   0x2000007e   Data           2  gps_app.o(.bss..L_MergedGlobals)
    longitude                                0x20000080   Data           4  gps_app.o(.bss..L_MergedGlobals)
    latitude                                 0x20000084   Data           4  gps_app.o(.bss..L_MergedGlobals)
    g_LatAndLongData                         0x20000098   Data          12  gps_app.o(.bss..L_MergedGlobals)
    nav_state                                0x200000a4   Data           1  navigation_app.o(.bss..L_MergedGlobals)
    Save_Data                                0x200000b0   Data         241  gps_app.o(.bss.Save_Data)
    USART_RX_BUF                             0x200001a4   Data         200  gps_app.o(.bss.USART_RX_BUF)
    current_navigation                       0x20000278   Data        7320  navigation_app.o(.bss.current_navigation)
    hdma_usart1_rx                           0x20001f10   Data          96  usart.o(.bss.hdma_usart1_rx)
    hdma_usart2_rx                           0x20001f70   Data          96  usart.o(.bss.hdma_usart2_rx)
    hdma_usart3_rx                           0x20001fd0   Data          96  usart.o(.bss.hdma_usart3_rx)
    hdma_usart6_rx                           0x20002030   Data          96  usart.o(.bss.hdma_usart6_rx)
    hi2c2                                    0x20002090   Data          84  i2c.o(.bss.hi2c2)
    htim1                                    0x200020e4   Data          72  tim.o(.bss.htim1)
    htim3                                    0x2000212c   Data          72  tim.o(.bss.htim3)
    htim4                                    0x20002174   Data          72  tim.o(.bss.htim4)
    huart1                                   0x2000234c   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20002394   Data          72  usart.o(.bss.huart2)
    huart3                                   0x200023dc   Data          72  usart.o(.bss.huart3)
    huart4                                   0x20002424   Data          72  usart.o(.bss.huart4)
    huart6                                   0x2000246c   Data          72  usart.o(.bss.huart6)
    ring_buffer                              0x200024b4   Data          12  uart_driver.o(.bss.ring_buffer)
    ring_buffer_input                        0x200024c0   Data         128  uart_driver.o(.bss.ring_buffer_input)
    task_num                                 0x20002540   Data           1  scheduler.o(.bss.task_num)
    uart2_data_buffer                        0x20002541   Data         512  uart2_driver.o(.bss.uart2_data_buffer)
    uart2_ring_buffer                        0x20002744   Data          12  uart2_driver.o(.bss.uart2_ring_buffer)
    uart2_ring_buffer_input                  0x20002750   Data         512  uart2_driver.o(.bss.uart2_ring_buffer_input)
    uart2_rx_dma_buffer                      0x20002950   Data         512  uart2_driver.o(.bss.uart2_rx_dma_buffer)
    uart3_data_buffer                        0x20002b50   Data         128  uart3_driver.o(.bss.uart3_data_buffer)
    uart3_ring_buffer                        0x20002bd0   Data          12  uart3_driver.o(.bss.uart3_ring_buffer)
    uart3_rx_dma_buffer                      0x20002bdc   Data         128  uart3_driver.o(.bss.uart3_rx_dma_buffer)
    uart6_data_buffer                        0x20002c5c   Data         512  uart6_driver.o(.bss.uart6_data_buffer)
    uart_data_buffer                         0x20002e5c   Data         128  uart_driver.o(.bss.uart_data_buffer)
    uart_rx_dma_buffer                       0x20002edc   Data         128  uart_driver.o(.bss.uart_rx_dma_buffer)
    uwTick                                   0x20003088   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20004090   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a818, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a7ac, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         1501  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1816    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1819    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1821    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1823    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1824    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1826    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1828    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1817    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         1504    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         1506    .text               mc_w.l(memcpya.o)
    0x08000246   0x08000246   0x00000024   Code   RO         1508    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x00000024   Code   RO         1510    .text               mc_w.l(strstr.o)
    0x0800028e   0x0800028e   0x00000018   Code   RO         1512    .text               mc_w.l(strncpy.o)
    0x080002a6   0x080002a6   0x00000014   Code   RO         1514    .text               mc_w.l(strchr.o)
    0x080002ba   0x080002ba   0x0000000e   Code   RO         1516    .text               mc_w.l(strlen.o)
    0x080002c8   0x080002c8   0x0000001c   Code   RO         1518    .text               mc_w.l(strcmp.o)
    0x080002e4   0x080002e4   0x0000001a   Code   RO         1520    .text               mc_w.l(memcmp.o)
    0x080002fe   0x080002fe   0x0000001e   Code   RO         1522    .text               mc_w.l(strncmp.o)
    0x0800031c   0x0800031c   0x0000014e   Code   RO         1787    .text               mf_w.l(dadd.o)
    0x0800046a   0x0800046a   0x000000de   Code   RO         1789    .text               mf_w.l(ddiv.o)
    0x08000548   0x08000548   0x0000001a   Code   RO         1791    .text               mf_w.l(dfltui.o)
    0x08000562   0x08000562   0x00000026   Code   RO         1793    .text               mf_w.l(f2d.o)
    0x08000588   0x08000588   0x00000038   Code   RO         1795    .text               mf_w.l(d2f.o)
    0x080005c0   0x080005c0   0x0000002c   Code   RO         1844    .text               mc_w.l(uidiv.o)
    0x080005ec   0x080005ec   0x0000001e   Code   RO         1846    .text               mc_w.l(llshl.o)
    0x0800060a   0x0800060a   0x00000020   Code   RO         1848    .text               mc_w.l(llushr.o)
    0x0800062a   0x0800062a   0x00000024   Code   RO         1850    .text               mc_w.l(llsshr.o)
    0x0800064e   0x0800064e   0x00000000   Code   RO         1859    .text               mc_w.l(iusefp.o)
    0x0800064e   0x0800064e   0x0000006e   Code   RO         1860    .text               mf_w.l(fepilogue.o)
    0x080006bc   0x080006bc   0x00000052   Code   RO         1862    .text               mf_w.l(frem.o)
    0x0800070e   0x0800070e   0x000000ba   Code   RO         1864    .text               mf_w.l(depilogue.o)
    0x080007c8   0x080007c8   0x000000e4   Code   RO         1866    .text               mf_w.l(dmul.o)
    0x080008ac   0x080008ac   0x00000030   Code   RO         1868    .text               mf_w.l(dfixul.o)
    0x080008dc   0x080008dc   0x00000030   Code   RO         1870    .text               mf_w.l(cdrcmple.o)
    0x0800090c   0x0800090c   0x00000030   Code   RO         1872    .text               mc_w.l(init.o)
    0x0800093c   0x0800093c   0x00000002   Code   RO          124    .text.BusFault_Handler  stm32f4xx_it.o
    0x0800093e   0x0800093e   0x00000002   PAD
    0x08000940   0x08000940   0x0000000c   Code   RO          136    .text.DMA1_Stream1_IRQHandler  stm32f4xx_it.o
    0x0800094c   0x0800094c   0x0000000c   Code   RO          138    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08000958   0x08000958   0x0000000c   Code   RO          148    .text.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08000964   0x08000964   0x0000000c   Code   RO          150    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000970   0x08000970   0x00000002   Code   RO          130    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000972   0x08000972   0x00000002   PAD
    0x08000974   0x08000974   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x0800097a   0x0800097a   0x00000002   PAD
    0x0800097c   0x0800097c   0x000001e4   Code   RO         1401    .text.GPS_Task      gps_app.o
    0x08000b60   0x08000b60   0x000002b0   Code   RO         1415    .text.GPS_Virtual_GenerateData  gps_app.o
    0x08000e10   0x08000e10   0x000000a0   Code   RO         1409    .text.GPS_Virtual_Init  gps_app.o
    0x08000eb0   0x08000eb0   0x0000008e   Code   RO          464    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x08000f3e   0x08000f3e   0x00000002   PAD
    0x08000f40   0x08000f40   0x00000024   Code   RO          466    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000f64   0x08000f64   0x000001c4   Code   RO          470    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08001128   0x08001128   0x00000162   Code   RO          456    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x0800128a   0x0800128a   0x00000002   PAD
    0x0800128c   0x0800128c   0x000000a2   Code   RO          462    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800132e   0x0800132e   0x00000002   PAD
    0x08001330   0x08001330   0x00000028   Code   RO          623    .text.HAL_Delay     stm32f4xx_hal.o
    0x08001358   0x08001358   0x0000019e   Code   RO          418    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x080014f6   0x080014f6   0x00000002   PAD
    0x080014f8   0x080014f8   0x00000010   Code   RO          426    .text.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x08001508   0x08001508   0x0000000a   Code   RO          424    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001512   0x08001512   0x00000002   PAD
    0x08001514   0x08001514   0x0000000c   Code   RO          615    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08001520   0x08001520   0x00000164   Code   RO          169    .text.HAL_I2C_Init  stm32f4xx_hal_i2c.o
    0x08001684   0x08001684   0x00000086   Code   RO           45    .text.HAL_I2C_MspInit  i2c.o
    0x0800170a   0x0800170a   0x00000002   PAD
    0x0800170c   0x0800170c   0x0000001a   Code   RO          613    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08001726   0x08001726   0x00000002   PAD
    0x08001728   0x08001728   0x00000036   Code   RO          603    .text.HAL_Init      stm32f4xx_hal.o
    0x0800175e   0x0800175e   0x00000002   PAD
    0x08001760   0x08001760   0x00000048   Code   RO          605    .text.HAL_InitTick  stm32f4xx_hal.o
    0x080017a8   0x080017a8   0x00000038   Code   RO          161    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x080017e0   0x080017e0   0x00000022   Code   RO          555    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001802   0x08001802   0x00000002   PAD
    0x08001804   0x08001804   0x00000056   Code   RO          553    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800185a   0x0800185a   0x00000002   PAD
    0x0800185c   0x0800185c   0x00000020   Code   RO          551    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x0800187c   0x0800187c   0x00000164   Code   RO          312    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080019e0   0x080019e0   0x00000026   Code   RO          324    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001a06   0x08001a06   0x00000002   PAD
    0x08001a08   0x08001a08   0x00000026   Code   RO          326    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001a2e   0x08001a2e   0x00000002   PAD
    0x08001a30   0x08001a30   0x0000006c   Code   RO          314    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001a9c   0x08001a9c   0x000003ac   Code   RO          310    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001e48   0x08001e48   0x0000002c   Code   RO          563    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001e74   0x08001e74   0x0000004c   Code   RO          990    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08001ec0   0x08001ec0   0x000000ba   Code   RO          988    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08001f7a   0x08001f7a   0x00000002   PAD
    0x08001f7c   0x08001f7c   0x0000005a   Code   RO          693    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08001fd6   0x08001fd6   0x00000002   PAD
    0x08001fd8   0x08001fd8   0x00000032   Code   RO           66    .text.HAL_TIM_Base_MspInit  tim.o
    0x0800200a   0x0800200a   0x00000002   PAD
    0x0800200c   0x0800200c   0x000001a0   Code   RO          873    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x080021ac   0x080021ac   0x000000b2   Code   RO          807    .text.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x0800225e   0x0800225e   0x00000002   PAD
    0x08002260   0x08002260   0x000000c0   Code   RO           68    .text.HAL_TIM_Encoder_MspInit  tim.o
    0x08002320   0x08002320   0x00000220   Code   RO          847    .text.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002540   0x08002540   0x0000005a   Code   RO          747    .text.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x0800259a   0x0800259a   0x00000002   PAD
    0x0800259c   0x0800259c   0x00000002   Code   RO          749    .text.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x0800259e   0x0800259e   0x00000002   PAD
    0x080025a0   0x080025a0   0x000001ee   Code   RO         1060    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800278e   0x0800278e   0x00000002   PAD
    0x08002790   0x08002790   0x00000094   Code   RO         1221    .text.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002824   0x08002824   0x00000064   Code   RO         1237    .text.HAL_UARTEx_RxEventCallback_UART2  uart2_driver.o
    0x08002888   0x08002888   0x00000060   Code   RO         1253    .text.HAL_UARTEx_RxEventCallback_UART3  uart3_driver.o
    0x080028e8   0x080028e8   0x0000021c   Code   RO         1054    .text.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002b04   0x08002b04   0x00000002   Code   RO         1096    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002b06   0x08002b06   0x00000002   PAD
    0x08002b08   0x08002b08   0x00000594   Code   RO         1090    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800309c   0x0800309c   0x00000060   Code   RO         1012    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x080030fc   0x080030fc   0x00000348   Code   RO           95    .text.HAL_UART_MspInit  usart.o
    0x08003444   0x08003444   0x00000002   Code   RO         1104    .text.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08003446   0x08003446   0x00000002   PAD
    0x08003448   0x08003448   0x00000002   Code   RO         1106    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800344a   0x0800344a   0x00000002   PAD
    0x0800344c   0x0800344c   0x00000192   Code   RO         1028    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080035de   0x080035de   0x00000002   PAD
    0x080035e0   0x080035e0   0x00000002   Code   RO         1100    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080035e2   0x080035e2   0x00000002   PAD
    0x080035e4   0x080035e4   0x00000002   Code   RO          120    .text.HardFault_Handler  stm32f4xx_it.o
    0x080035e6   0x080035e6   0x00000002   PAD
    0x080035e8   0x080035e8   0x0000007c   Code   RO           34    .text.MX_DMA_Init   dma.o
    0x08003664   0x08003664   0x00000152   Code   RO           25    .text.MX_GPIO_Init  gpio.o
    0x080037b6   0x080037b6   0x00000002   PAD
    0x080037b8   0x080037b8   0x00000042   Code   RO           43    .text.MX_I2C2_Init  i2c.o
    0x080037fa   0x080037fa   0x00000002   PAD
    0x080037fc   0x080037fc   0x00000166   Code   RO           58    .text.MX_TIM1_Init  tim.o
    0x08003962   0x08003962   0x00000002   PAD
    0x08003964   0x08003964   0x00000068   Code   RO           62    .text.MX_TIM3_Init  tim.o
    0x080039cc   0x080039cc   0x00000068   Code   RO           64    .text.MX_TIM4_Init  tim.o
    0x08003a34   0x08003a34   0x0000003c   Code   RO           85    .text.MX_UART4_Init  usart.o
    0x08003a70   0x08003a70   0x0000003c   Code   RO           87    .text.MX_USART1_UART_Init  usart.o
    0x08003aac   0x08003aac   0x0000003c   Code   RO           89    .text.MX_USART2_UART_Init  usart.o
    0x08003ae8   0x08003ae8   0x0000003c   Code   RO           91    .text.MX_USART3_UART_Init  usart.o
    0x08003b24   0x08003b24   0x0000003c   Code   RO           93    .text.MX_USART6_UART_Init  usart.o
    0x08003b60   0x08003b60   0x00000002   Code   RO          122    .text.MemManage_Handler  stm32f4xx_it.o
    0x08003b62   0x08003b62   0x00000002   PAD
    0x08003b64   0x08003b64   0x00000002   Code   RO          118    .text.NMI_Handler   stm32f4xx_it.o
    0x08003b66   0x08003b66   0x00000002   PAD
    0x08003b68   0x08003b68   0x0000012e   Code   RO         1438    .text.Navigation_FindDestination  navigation_app.o
    0x08003c96   0x08003c96   0x00000002   PAD
    0x08003c98   0x08003c98   0x0000035c   Code   RO         1440    .text.Navigation_PlanRoute  navigation_app.o
    0x08003ff4   0x08003ff4   0x000000e0   Code   RO         1446    .text.Navigation_PrintDestinations  navigation_app.o
    0x080040d4   0x080040d4   0x00000128   Code   RO         1448    .text.Navigation_PrintStatus  navigation_app.o
    0x080041fc   0x080041fc   0x0000026c   Code   RO         1444    .text.Navigation_ProcessCommand  navigation_app.o
    0x08004468   0x08004468   0x00000168   Code   RO         1436    .text.Navigation_StartNavigation  navigation_app.o
    0x080045d0   0x080045d0   0x00000070   Code   RO         1432    .text.Navigation_Task  navigation_app.o
    0x08004640   0x08004640   0x00000328   Code   RO         1434    .text.Navigation_UpdateProgress  navigation_app.o
    0x08004968   0x08004968   0x00000002   Code   RO          132    .text.PendSV_Handler  stm32f4xx_it.o
    0x0800496a   0x0800496a   0x00000002   PAD
    0x0800496c   0x0800496c   0x00000002   Code   RO          128    .text.SVC_Handler   stm32f4xx_it.o
    0x0800496e   0x0800496e   0x00000002   PAD
    0x08004970   0x08004970   0x00000020   Code   RO         1285    .text.Scheduler_Init  scheduler.o
    0x08004990   0x08004990   0x0000004a   Code   RO         1287    .text.Scheduler_Run  scheduler.o
    0x080049da   0x080049da   0x00000002   PAD
    0x080049dc   0x080049dc   0x00000004   Code   RO          134    .text.SysTick_Handler  stm32f4xx_it.o
    0x080049e0   0x080049e0   0x000000a8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08004a88   0x08004a88   0x00000012   Code   RO         1134    .text.SystemInit    system_stm32f4xx.o
    0x08004a9a   0x08004a9a   0x00000002   PAD
    0x08004a9c   0x08004a9c   0x0000013a   Code   RO          697    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08004bd6   0x08004bd6   0x00000002   PAD
    0x08004bd8   0x08004bd8   0x0000000c   Code   RO          146    .text.UART4_IRQHandler  stm32f4xx_it.o
    0x08004be4   0x08004be4   0x0000000a   Code   RO         1094    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08004bee   0x08004bee   0x00000002   PAD
    0x08004bf0   0x08004bf0   0x0000017c   Code   RO         1044    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x08004d6c   0x08004d6c   0x0000015e   Code   RO         1122    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08004eca   0x08004eca   0x00000002   PAD
    0x08004ecc   0x08004ecc   0x00000018   Code   RO         1124    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08004ee4   0x08004ee4   0x000000fe   Code   RO         1092    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004fe2   0x08004fe2   0x00000002   PAD
    0x08004fe4   0x08004fe4   0x000000e6   Code   RO         1016    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x080050ca   0x080050ca   0x00000002   PAD
    0x080050cc   0x080050cc   0x0000000c   Code   RO          140    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x080050d8   0x080050d8   0x0000000c   Code   RO          142    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x080050e4   0x080050e4   0x0000000c   Code   RO          144    .text.USART3_IRQHandler  stm32f4xx_it.o
    0x080050f0   0x080050f0   0x0000000c   Code   RO          152    .text.USART6_IRQHandler  stm32f4xx_it.o
    0x080050fc   0x080050fc   0x00000046   Code   RO         1314    .text.Uart2_Init    uart2_app.o
    0x08005142   0x08005142   0x00000002   PAD
    0x08005144   0x08005144   0x0000006c   Code   RO         1235    .text.Uart2_Printf  uart2_driver.o
    0x080051b0   0x080051b0   0x000004e0   Code   RO         1316    .text.Uart2_Task    uart2_app.o
    0x08005690   0x08005690   0x0000000e   Code   RO         1330    .text.Uart3_Task    uart3_app.o
    0x0800569e   0x0800569e   0x00000002   PAD
    0x080056a0   0x080056a0   0x00000094   Code   RO         1468    .text.Uart6_Task    uart6_app.o
    0x08005734   0x08005734   0x00000094   Code   RO         1299    .text.Uart_Init     uart_app.o
    0x080057c8   0x080057c8   0x0000065c   Code   RO         1301    .text.Uart_Task     uart_app.o
    0x08005e24   0x08005e24   0x00000002   Code   RO          126    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08005e26   0x08005e26   0x00000002   PAD
    0x08005e28   0x08005e28   0x000001a4   Code   RO         1377    .text.esp01_CheckAndRecoverTCPConnection  esp01_app.o
    0x08005fcc   0x08005fcc   0x000002dc   Code   RO         1353    .text.esp01_EstablishTCPConnection  esp01_app.o
    0x080062a8   0x080062a8   0x000000fc   Code   RO         1381    .text.esp01_ForceReset  esp01_app.o
    0x080063a4   0x080063a4   0x000000ac   Code   RO         1369    .text.esp01_GetRealLocation  esp01_app.o
    0x08006450   0x08006450   0x0000000c   Code   RO         1349    .text.esp01_GetState  esp01_app.o
    0x0800645c   0x0800645c   0x000000f8   Code   RO         1339    .text.esp01_Init    esp01_app.o
    0x08006554   0x08006554   0x00000190   Code   RO         1343    .text.esp01_InitSequence  esp01_app.o
    0x080066e4   0x080066e4   0x0000013c   Code   RO         1379    .text.esp01_NetworkDiagnostics  esp01_app.o
    0x08006820   0x08006820   0x0000004c   Code   RO         1361    .text.esp01_Reset   esp01_app.o
    0x0800686c   0x0800686c   0x00000012   Code   RO         1359    .text.esp01_ResetTCPState  esp01_app.o
    0x0800687e   0x0800687e   0x00000002   PAD
    0x08006880   0x08006880   0x000001b4   Code   RO         1371    .text.esp01_SendDataWithRecovery  esp01_app.o
    0x08006a34   0x08006a34   0x00000004   Code   RO         1373    .text.esp01_SendLocationData  esp01_app.o
    0x08006a38   0x08006a38   0x000000ac   Code   RO         1351    .text.esp01_SetConnected  esp01_app.o
    0x08006ae4   0x08006ae4   0x00000038   Code   RO         1357    .text.esp01_SetDataSendReady  esp01_app.o
    0x08006b1c   0x08006b1c   0x00000048   Code   RO         1355    .text.esp01_SetTCPConnected  esp01_app.o
    0x08006b64   0x08006b64   0x00000060   Code   RO         1383    .text.esp01_StartInit  esp01_app.o
    0x08006bc4   0x08006bc4   0x00000144   Code   RO         1341    .text.esp01_Task    esp01_app.o
    0x08006d08   0x08006d08   0x00000168   Code   RO         1367    .text.esp01_TryTCPWithIP  esp01_app.o
    0x08006e70   0x08006e70   0x0000029c   Code   RO         1345    .text.esp01_UploadGPSData  esp01_app.o
    0x0800710c   0x0800710c   0x0000003e   Code   RO           11    .text.main          main.o
    0x0800714a   0x0800714a   0x00000002   PAD
    0x0800714c   0x0800714c   0x0000003e   Code   RO           99    .text.my_printf     usart.o
    0x0800718a   0x0800718a   0x00000006   PAD
    0x08007190   0x08007190   0x00000528   Code   RO         1403    .text.parseGpsBuffer  gps_app.o
    0x080076b8   0x080076b8   0x00000044   Code   RO         1195    .text.rt_ringbuffer_data_len  ringbuffer.o
    0x080076fc   0x080076fc   0x000000b8   Code   RO         1199    .text.rt_ringbuffer_get  ringbuffer.o
    0x080077b4   0x080077b4   0x0000000e   Code   RO         1191    .text.rt_ringbuffer_init  ringbuffer.o
    0x080077c2   0x080077c2   0x00000002   PAD
    0x080077c4   0x080077c4   0x000000b2   Code   RO         1193    .text.rt_ringbuffer_put  ringbuffer.o
    0x08007876   0x08007876   0x00000002   PAD
    0x08007878   0x08007878   0x00000034   Code   RO         1760    i.__0snprintf       mc_w.l(printfa.o)
    0x080078ac   0x080078ac   0x00000034   Code   RO         1764    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080078e0   0x080078e0   0x00000026   Code   RO         1797    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08007906   0x08007906   0x00000002   PAD
    0x08007908   0x08007908   0x000002ac   Code   RO         1477    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08007bb4   0x08007bb4   0x00000150   Code   RO         1483    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08007d04   0x08007d04   0x000000b0   Code   RO         1489    i.__hardfp_fmodf    m_wm.l(fmodf.o)
    0x08007db4   0x08007db4   0x00000190   Code   RO         1495    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08007f44   0x08007f44   0x00000006   Code   RO         1800    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08007f4a   0x08007f4a   0x00000006   Code   RO         1801    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x08007f50   0x08007f50   0x00000010   Code   RO         1802    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08007f60   0x08007f60   0x00000010   Code   RO         1805    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08007f70   0x08007f70   0x00000154   Code   RO         1813    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x080080c4   0x080080c4   0x0000000e   Code   RO         1878    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080080d2   0x080080d2   0x00000002   Code   RO         1879    i.__scatterload_null  mc_w.l(handlers.o)
    0x080080d4   0x080080d4   0x0000000e   Code   RO         1880    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080080e2   0x080080e2   0x00000002   PAD
    0x080080e4   0x080080e4   0x0000000c   Code   RO         1854    i.__set_errno       mc_w.l(errno.o)
    0x080080f0   0x080080f0   0x00000184   Code   RO         1766    i._fp_digits        mc_w.l(printfa.o)
    0x08008274   0x08008274   0x000006dc   Code   RO         1767    i._printf_core      mc_w.l(printfa.o)
    0x08008950   0x08008950   0x00000024   Code   RO         1768    i._printf_post_padding  mc_w.l(printfa.o)
    0x08008974   0x08008974   0x0000002e   Code   RO         1769    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080089a2   0x080089a2   0x00000016   Code   RO         1770    i._snputc           mc_w.l(printfa.o)
    0x080089b8   0x080089b8   0x00000020   Data   RO         1814    .constdata          m_wm.l(rredf.o)
    0x080089d8   0x080089d8   0x00000010   Data   RO         1139    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x080089e8   0x080089e8   0x00000008   Data   RO         1140    .rodata.APBPrescTable  system_stm32f4xx.o
    0x080089f0   0x080089f0   0x00000008   Data   RO          480    .rodata.cst8        stm32f4xx_hal_dma.o
    0x080089f8   0x080089f8   0x00000820   Data   RO         1456    .rodata.destinations  navigation_app.o
    0x08009218   0x08009218   0x00000519   Data   RO         1305    .rodata.str1.1      uart_app.o
    0x08009731   0x08009731   0x00000408   Data   RO         1319    .rodata.str1.1      uart2_app.o
    0x08009b39   0x08009b39   0x0000096b   Data   RO         1385    .rodata.str1.1      esp01_app.o
    0x0800a4a4   0x0800a4a4   0x0000010a   Data   RO         1420    .rodata.str1.1      gps_app.o
    0x0800a5ae   0x0800a5ae   0x000001db   Data   RO         1455    .rodata.str1.1      navigation_app.o
    0x0800a789   0x0800a789   0x00000003   PAD
    0x0800a78c   0x0800a78c   0x00000020   Data   RO         1877    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a7b0, Size: 0x00004090, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800a7b0   0x00000004   Data   RW         1855    .data               mc_w.l(errno.o)
    0x20000004   0x0800a7b4   0x00000008   Data   RW          658    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x2000000c   0x0800a7bc   0x00000004   Data   RW         1138    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000010   0x0800a7c0   0x00000054   Data   RW         1290    .data.scheduler_task  scheduler.o
    0x20000064   0x0800a814   0x00000004   PAD
    0x20000068        -       0x00000010   Zero   RW         1388    .bss..L_MergedGlobals  esp01_app.o
    0x20000078        -       0x0000002c   Zero   RW         1421    .bss..L_MergedGlobals  gps_app.o
    0x200000a4        -       0x0000000c   Zero   RW         1457    .bss..L_MergedGlobals  navigation_app.o
    0x200000b0        -       0x000000f1   Zero   RW         1418    .bss.Save_Data      gps_app.o
    0x200001a1   0x0800a814   0x00000003   PAD
    0x200001a4        -       0x000000c8   Zero   RW         1417    .bss.USART_RX_BUF   gps_app.o
    0x2000026c        -       0x00000004   Zero   RW         1318    .bss.Uart2_Task.last_receive_time  uart2_app.o
    0x20000270        -       0x00000001   Zero   RW         1303    .bss.Uart_Init.init_done  uart_app.o
    0x20000271   0x0800a814   0x00000003   PAD
    0x20000274        -       0x00000004   Zero   RW         1304    .bss.Uart_Task.last_heartbeat  uart_app.o
    0x20000278        -       0x00001c98   Zero   RW         1454    .bss.current_navigation  navigation_app.o
    0x20001f10        -       0x00000060   Zero   RW          106    .bss.hdma_usart1_rx  usart.o
    0x20001f70        -       0x00000060   Zero   RW          107    .bss.hdma_usart2_rx  usart.o
    0x20001fd0        -       0x00000060   Zero   RW          108    .bss.hdma_usart3_rx  usart.o
    0x20002030        -       0x00000060   Zero   RW          109    .bss.hdma_usart6_rx  usart.o
    0x20002090        -       0x00000054   Zero   RW           49    .bss.hi2c2          i2c.o
    0x200020e4        -       0x00000048   Zero   RW           74    .bss.htim1          tim.o
    0x2000212c        -       0x00000048   Zero   RW           75    .bss.htim3          tim.o
    0x20002174        -       0x00000048   Zero   RW           76    .bss.htim4          tim.o
    0x200021bc        -       0x00000190   Zero   RW         1387    .bss.http_request_buffer  esp01_app.o
    0x2000234c        -       0x00000048   Zero   RW          102    .bss.huart1         usart.o
    0x20002394        -       0x00000048   Zero   RW          103    .bss.huart2         usart.o
    0x200023dc        -       0x00000048   Zero   RW          104    .bss.huart3         usart.o
    0x20002424        -       0x00000048   Zero   RW          101    .bss.huart4         usart.o
    0x2000246c        -       0x00000048   Zero   RW          105    .bss.huart6         usart.o
    0x200024b4        -       0x0000000c   Zero   RW         1223    .bss.ring_buffer    uart_driver.o
    0x200024c0        -       0x00000080   Zero   RW         1225    .bss.ring_buffer_input  uart_driver.o
    0x20002540        -       0x00000001   Zero   RW         1289    .bss.task_num       scheduler.o
    0x20002541        -       0x00000200   Zero   RW         1242    .bss.uart2_data_buffer  uart2_driver.o
    0x20002741   0x0800a814   0x00000003   PAD
    0x20002744        -       0x0000000c   Zero   RW         1239    .bss.uart2_ring_buffer  uart2_driver.o
    0x20002750        -       0x00000200   Zero   RW         1241    .bss.uart2_ring_buffer_input  uart2_driver.o
    0x20002950        -       0x00000200   Zero   RW         1240    .bss.uart2_rx_dma_buffer  uart2_driver.o
    0x20002b50        -       0x00000080   Zero   RW         1258    .bss.uart3_data_buffer  uart3_driver.o
    0x20002bd0        -       0x0000000c   Zero   RW         1255    .bss.uart3_ring_buffer  uart3_driver.o
    0x20002bdc        -       0x00000080   Zero   RW         1256    .bss.uart3_rx_dma_buffer  uart3_driver.o
    0x20002c5c        -       0x00000200   Zero   RW         1274    .bss.uart6_data_buffer  uart6_driver.o
    0x20002e5c        -       0x00000080   Zero   RW         1226    .bss.uart_data_buffer  uart_driver.o
    0x20002edc        -       0x00000080   Zero   RW         1224    .bss.uart_rx_dma_buffer  uart_driver.o
    0x20002f5c        -       0x0000012c   Zero   RW         1386    .bss.url_params_buffer  esp01_app.o
    0x20003088        -       0x00000004   Zero   RW          657    .bss.uwTick         stm32f4xx_hal.o
    0x2000308c   0x0800a814   0x00000004   PAD
    0x20003090        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800a818, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       124          0          0          0          0       3546   dma.o
      4834       1526       2411          0        716      13215   esp01_app.o
       338          0          0          0          0       2339   gpio.o
      2652         96        266          0        485      12589   gps_app.o
       200          0          0          0         84       5281   i2c.o
       236          0          0          0          0       2950   main.o
      3582        676       2555          0       7332      13700   navigation_app.o
       444          0          0          0          0       6568   ringbuffer.o
       106          0          0         84          1       1479   scheduler.o
        36          8        392          0       4096        852   startup_stm32f407xx.o
       204          0          0          8          4       7340   stm32f4xx_hal.o
       196          0          0          0          0      10757   stm32f4xx_hal_cortex.o
      1146          6          8          0          0      10569   stm32f4xx_hal_dma.o
       440          0          0          0          0       5387   stm32f4xx_hal_gpio.o
       356          0          0          0          0      44441   stm32f4xx_hal_i2c.o
        56          0          0          0          0       1495   stm32f4xx_hal_msp.o
      1480          0          0          0          0       7511   stm32f4xx_hal_rcc.o
      1634          6          0          0          0      57465   stm32f4xx_hal_tim.o
       262          0          0          0          0      21522   stm32f4xx_hal_tim_ex.o
      4216          0          0          0          0      32280   stm32f4xx_hal_uart.o
       128          0          0          0          0       4873   stm32f4xx_it.o
        18          0         24          4          0       2624   system_stm32f4xx.o
       808          0          0          0        216       9505   tim.o
      1318        144       1032          0          4       2941   uart2_app.o
       208         28          0          0       1548       4692   uart2_driver.o
        14          0          0          0          0        588   uart3_app.o
        96          0          0          0        268       4617   uart3_driver.o
       148         36          0          0          0       1652   uart6_app.o
         0          0          0          0        512       4526   uart6_driver.o
      1776        196       1305          0          5       4269   uart_app.o
       148          0          0          0        396       5020   uart_driver.o
      1202          0          0          0        744       9149   usart.o

    ----------------------------------------------------------------------
     28512       <USER>       <GROUP>         96      16428     315742   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       106          0          3          0         17          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       684         90          0          0          0        208   atan2f.o
       336         56          0          0          0        136   cosf.o
       176          0          0          0          0        168   fmodf.o
        38          0          0          0          0        116   fpclassifyf.o
        44         12          0          0          0        464   funder.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2352         90          0          0          0        604   printfa.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        82          0          0          0          0         96   frem.o

    ----------------------------------------------------------------------
      6352        <USER>         <GROUP>          4          0       4436   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2018        238         32          0          0       1464   m_wm.l
      2952        114          0          4          0       1820   mc_w.l
      1378          0          0          0          0       1152   mf_w.l

    ----------------------------------------------------------------------
      6352        <USER>         <GROUP>          4          0       4436   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     34864       3074       8060        100      16428     317290   Grand Totals
     34864       3074       8060        100      16428     317290   ELF Image Totals
     34864       3074       8060        100          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                42924 (  41.92kB)
    Total RW  Size (RW Data + ZI Data)             16528 (  16.14kB)
    Total ROM Size (Code + RO Data + RW Data)      43024 (  42.02kB)

==============================================================================

