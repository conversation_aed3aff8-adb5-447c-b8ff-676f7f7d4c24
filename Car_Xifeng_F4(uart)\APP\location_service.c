#include "location_service.h"
#include <math.h>

/* 全局定位服务实例 */
LocationService_t location_service = {0};

/* 私有函数声明 */
static void LocationService_AddToHistory(GPS_Coordinate_t* location);
static uint8_t LocationService_BuildCloudMessage(char* buffer, size_t buffer_size);
static uint8_t LocationService_BuildEmergencyMessage(char* buffer, size_t buffer_size);

/**
 * @brief 初始化定位服务
 */
uint8_t LocationService_Init(void)
{
    memset(&location_service, 0, sizeof(LocationService_t));
    
    location_service.last_update_time = HAL_GetTick();
    location_service.last_cloud_sync_time = HAL_GetTick();
    location_service.emergency_mode = 0;
    
    // 设置默认的华为云配置 (需要根据你的实际配置修改)
    strcpy(location_service.cloud_config.device_id, "your_device_id");
    strcpy(location_service.cloud_config.product_id, "your_product_id");
    strcpy(location_service.cloud_config.secret, "your_device_secret");
    
    my_printf(&huart1, "Location Service Initialized\r\n");
    return 0;
}

/**
 * @brief 设置华为云配置
 */
uint8_t LocationService_SetCloudConfig(const char* device_id, const char* product_id, const char* secret)
{
    if (!device_id || !product_id || !secret) {
        return 1;
    }
    
    strncpy(location_service.cloud_config.device_id, device_id, sizeof(location_service.cloud_config.device_id) - 1);
    strncpy(location_service.cloud_config.product_id, product_id, sizeof(location_service.cloud_config.product_id) - 1);
    strncpy(location_service.cloud_config.secret, secret, sizeof(location_service.cloud_config.secret) - 1);
    
    my_printf(&huart1, "Cloud config updated: Device=%s, Product=%s\r\n", device_id, product_id);
    return 0;
}

/**
 * @brief 更新位置信息
 */
uint8_t LocationService_UpdateLocation(double lat, double lon, float alt, float accuracy)
{
    GPS_Coordinate_t new_location;
    new_location.latitude = lat;
    new_location.longitude = lon;
    new_location.altitude = alt;
    new_location.accuracy = accuracy;
    new_location.timestamp = HAL_GetTick();
    new_location.valid = 1;
    
    // 更新当前位置
    location_service.current_location = new_location;
    location_service.last_update_time = HAL_GetTick();
    location_service.total_updates++;
    
    // 添加到历史记录
    LocationService_AddToHistory(&new_location);
    
    // my_printf(&huart1, "Location updated: %.6f, %.6f (±%.1fm)\r\n", lat, lon, accuracy);  // 已注释掉位置更新信息
    return 0;
}

/**
 * @brief 获取当前位置
 */
GPS_Coordinate_t* LocationService_GetCurrentLocation(void)
{
    return &location_service.current_location;
}

/**
 * @brief 检查位置数据是否有效
 */
uint8_t LocationService_IsLocationValid(void)
{
    return location_service.current_location.valid;
}

/**
 * @brief 连接到华为云
 */
uint8_t LocationService_ConnectToCloud(void)
{
    // 这里实现华为云连接逻辑
    // 实际实现需要根据华为云IoT平台的具体API
    
    my_printf(&huart1, "Connecting to Huawei Cloud...\r\n");
    
    // 模拟连接过程
    HAL_Delay(1000);
    
    location_service.cloud_config.connected = 1;
    my_printf(&huart1, "Connected to Huawei Cloud successfully\r\n");
    
    return 0;
}

/**
 * @brief 同步数据到华为云
 */
uint8_t LocationService_SyncToCloud(void)
{
    if (!location_service.cloud_config.connected) {
        // my_printf(&huart1, "Cloud not connected, attempting to connect...\r\n");  // 已注释掉云连接信息
        if (LocationService_ConnectToCloud() != 0) {
            return 1;
        }
    }
    
    if (!LocationService_IsLocationValid()) {
        // my_printf(&huart1, "No valid location data to sync\r\n");  // 已注释掉无效位置数据信息
        return 1;
    }
    
    char message[512];
    if (LocationService_BuildCloudMessage(message, sizeof(message)) != 0) {
        return 1;
    }
    
    // 通过串口2发送到华为云 (假设通过WiFi模块或4G模块)
    Uart2_Printf(&huart2, "%s\r\n", message);
    
    location_service.last_cloud_sync_time = HAL_GetTick();
    location_service.cloud_sync_count++;
    
    // my_printf(&huart1, "Location synced to cloud\r\n");  // 已注释掉云同步成功信息
    return 0;
}

/**
 * @brief 发送紧急警报
 */
uint8_t LocationService_SendEmergencyAlert(Emergency_Type_t type, const char* description)
{
    if (!location_service.cloud_config.connected) {
        LocationService_ConnectToCloud();
    }
    
    // 设置紧急事件
    location_service.current_emergency.type = type;
    location_service.current_emergency.location = location_service.current_location;
    location_service.current_emergency.timestamp = HAL_GetTick();
    strncpy(location_service.current_emergency.description, description, 
            sizeof(location_service.current_emergency.description) - 1);
    
    location_service.emergency_mode = 1;
    location_service.emergency_count++;
    
    char emergency_message[512];
    if (LocationService_BuildEmergencyMessage(emergency_message, sizeof(emergency_message)) != 0) {
        return 1;
    }
    
    // 发送紧急消息
    Uart2_Printf(&huart2, "%s\r\n", emergency_message);
    
    location_service.current_emergency.sent_to_cloud = 1;
    
    my_printf(&huart1, "EMERGENCY ALERT SENT: %s\r\n", description);
    return 0;
}

/**
 * @brief 触发紧急事件
 */
uint8_t LocationService_TriggerEmergency(Emergency_Type_t type, const char* description)
{
    return LocationService_SendEmergencyAlert(type, description);
}

/**
 * @brief 清除紧急状态
 */
uint8_t LocationService_ClearEmergency(void)
{
    location_service.emergency_mode = 0;
    memset(&location_service.current_emergency, 0, sizeof(Emergency_Event_t));

    my_printf(&huart1, "Emergency cleared\r\n");
    return 0;
}

/**
 * @brief 检查是否处于紧急状态
 */
uint8_t LocationService_IsEmergencyActive(void)
{
    return location_service.emergency_mode;
}

/**
 * @brief 生成OpenStreetMap URL
 */
void LocationService_GenerateMapURL(char* url_buffer, size_t buffer_size)
{
    if (!LocationService_IsLocationValid()) {
        strcpy(url_buffer, "No valid location data");
        return;
    }

    GPS_Coordinate_t* loc = &location_service.current_location;

    // 生成OpenStreetMap URL (可以在浏览器中打开)
    snprintf(url_buffer, buffer_size,
             "https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=16#map=16/%.6f/%.6f",
             loc->latitude, loc->longitude, loc->latitude, loc->longitude);
}

/**
 * @brief 生成紧急情况地图URL
 */
void LocationService_GenerateEmergencyMapURL(char* url_buffer, size_t buffer_size)
{
    if (!LocationService_IsLocationValid()) {
        strcpy(url_buffer, "No valid location data");
        return;
    }

    GPS_Coordinate_t* loc = &location_service.current_location;

    // 生成带有紧急标记的地图URL
    snprintf(url_buffer, buffer_size,
             "https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=18#map=18/%.6f/%.6f&emergency=true",
             loc->latitude, loc->longitude, loc->latitude, loc->longitude);
}

/**
 * @brief 定位服务主任务
 */
void LocationService_Task(void)
{
    uint32_t current_time = HAL_GetTick();
    uint32_t update_interval = location_service.emergency_mode ?
                              EMERGENCY_UPDATE_INTERVAL_MS : LOCATION_UPDATE_INTERVAL_MS;

    // 定期同步到云端
    if (current_time - location_service.last_cloud_sync_time > update_interval) {
        if (LocationService_IsLocationValid()) {
            LocationService_SyncToCloud();
        }
    }

    // 在紧急模式下更频繁地发送位置信息
    if (location_service.emergency_mode) {
        static uint32_t last_emergency_sync = 0;
        if (current_time - last_emergency_sync > EMERGENCY_UPDATE_INTERVAL_MS) {
            LocationService_SyncToCloud();
            last_emergency_sync = current_time;
        }
    }
}

/**
 * @brief 打印定位服务状态
 */
void LocationService_PrintStatus(void)
{
    // my_printf(&huart1, "=== Location Service Status ===\r\n");  // 已注释掉定位服务状态标题

    if (LocationService_IsLocationValid()) {
        GPS_Coordinate_t* loc = &location_service.current_location;
        // my_printf(&huart1, "Current Location: %.6f, %.6f\r\n", loc->latitude, loc->longitude);  // 已注释掉当前位置
        // my_printf(&huart1, "Altitude: %.1f m, Accuracy: ±%.1f m\r\n", loc->altitude, loc->accuracy);  // 已注释掉海拔和精度
        // my_printf(&huart1, "Last Update: %lu ms ago\r\n", HAL_GetTick() - loc->timestamp);  // 已注释掉最后更新时间

        // 生成并显示地图URL
        char map_url[256];
        LocationService_GenerateMapURL(map_url, sizeof(map_url));
        // my_printf(&huart1, "Map URL: %s\r\n", map_url);  // 已注释掉地图URL
    } else {
        // my_printf(&huart1, "No valid location data\r\n");  // 已注释掉无效位置数据信息
    }

    // my_printf(&huart1, "Cloud Connected: %s\r\n",
    //           location_service.cloud_config.connected ? "YES" : "NO");  // 已注释掉云连接状态
    // my_printf(&huart1, "Emergency Mode: %s\r\n",
    //           location_service.emergency_mode ? "ACTIVE" : "NORMAL");  // 已注释掉紧急模式状态
    // my_printf(&huart1, "Total Updates: %lu\r\n", location_service.total_updates);  // 已注释掉总更新次数
    // my_printf(&huart1, "Cloud Syncs: %lu\r\n", location_service.cloud_sync_count);  // 已注释掉云同步次数
    my_printf(&huart1, "Emergency Count: %lu\r\n", location_service.emergency_count);
    my_printf(&huart1, "==============================\r\n");
}

/**
 * @brief 打印位置历史记录
 */
void LocationService_PrintLocationHistory(void)
{
    my_printf(&huart1, "=== Location History ===\r\n");

    if (location_service.history_count == 0) {
        my_printf(&huart1, "No location history\r\n");
        return;
    }

    for (uint8_t i = 0; i < location_service.history_count; i++) {
        GPS_Coordinate_t* loc = &location_service.location_history[i];
        my_printf(&huart1, "%d: %.6f, %.6f (%.1fm) - %lu ms ago\r\n",
                  i + 1, loc->latitude, loc->longitude, loc->accuracy,
                  HAL_GetTick() - loc->timestamp);
    }

    my_printf(&huart1, "========================\r\n");
}

/**
 * @brief 计算两点间距离 (米)
 */
float LocationService_CalculateDistance(GPS_Coordinate_t* pos1, GPS_Coordinate_t* pos2)
{
    if (!pos1 || !pos2 || !pos1->valid || !pos2->valid) {
        return -1.0f;
    }

    // 使用Haversine公式计算距离
    const float R = 6371000.0f; // 地球半径 (米)

    float lat1_rad = pos1->latitude * M_PI / 180.0f;
    float lat2_rad = pos2->latitude * M_PI / 180.0f;
    float dlat_rad = (pos2->latitude - pos1->latitude) * M_PI / 180.0f;
    float dlon_rad = (pos2->longitude - pos1->longitude) * M_PI / 180.0f;

    float a = sinf(dlat_rad / 2) * sinf(dlat_rad / 2) +
              cosf(lat1_rad) * cosf(lat2_rad) *
              sinf(dlon_rad / 2) * sinf(dlon_rad / 2);

    float c = 2 * atan2f(sqrtf(a), sqrtf(1 - a));

    return R * c;
}

/**
 * @brief 检查是否在移动
 */
uint8_t LocationService_IsMoving(float threshold_meters)
{
    if (location_service.history_count < 2) {
        return 0;
    }

    GPS_Coordinate_t* current = &location_service.current_location;
    GPS_Coordinate_t* previous = &location_service.location_history[
        (location_service.history_index - 1 + MAX_LOCATION_HISTORY) % MAX_LOCATION_HISTORY];

    float distance = LocationService_CalculateDistance(current, previous);

    return (distance > threshold_meters) ? 1 : 0;
}

/* 私有函数实现 */

/**
 * @brief 添加位置到历史记录
 */
static void LocationService_AddToHistory(GPS_Coordinate_t* location)
{
    location_service.location_history[location_service.history_index] = *location;
    location_service.history_index = (location_service.history_index + 1) % MAX_LOCATION_HISTORY;

    if (location_service.history_count < MAX_LOCATION_HISTORY) {
        location_service.history_count++;
    }
}

/**
 * @brief 构建云端消息
 */
static uint8_t LocationService_BuildCloudMessage(char* buffer, size_t buffer_size)
{
    if (!buffer || !LocationService_IsLocationValid()) {
        return 1;
    }

    GPS_Coordinate_t* loc = &location_service.current_location;

    // 构建JSON格式的消息 (华为云IoT平台格式)
    snprintf(buffer, buffer_size,
             "{"
             "\"device_id\":\"%s\","
             "\"product_id\":\"%s\","
             "\"timestamp\":%lu,"
             "\"location\":{"
             "\"latitude\":%.6f,"
             "\"longitude\":%.6f,"
             "\"altitude\":%.1f,"
             "\"accuracy\":%.1f"
             "},"
             "\"status\":\"normal\""
             "}",
             location_service.cloud_config.device_id,
             location_service.cloud_config.product_id,
             loc->timestamp,
             loc->latitude,
             loc->longitude,
             loc->altitude,
             loc->accuracy);

    return 0;
}

/**
 * @brief 构建紧急消息
 */
static uint8_t LocationService_BuildEmergencyMessage(char* buffer, size_t buffer_size)
{
    if (!buffer) {
        return 1;
    }

    Emergency_Event_t* emergency = &location_service.current_emergency;

    const char* emergency_types[] = {
        "none", "fall_detected", "fall_confirmed", "sos_button", "low_battery", "device_offline"
    };

    // 构建紧急事件JSON消息
    snprintf(buffer, buffer_size,
             "{"
             "\"device_id\":\"%s\","
             "\"product_id\":\"%s\","
             "\"timestamp\":%lu,"
             "\"emergency\":{"
             "\"type\":\"%s\","
             "\"description\":\"%s\","
             "\"location\":{"
             "\"latitude\":%.6f,"
             "\"longitude\":%.6f,"
             "\"altitude\":%.1f"
             "},"
             "\"map_url\":\"https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=18\""
             "},"
             "\"status\":\"emergency\""
             "}",
             location_service.cloud_config.device_id,
             location_service.cloud_config.product_id,
             emergency->timestamp,
             emergency_types[emergency->type],
             emergency->description,
             emergency->location.latitude,
             emergency->location.longitude,
             emergency->location.altitude,
             emergency->location.latitude,
             emergency->location.longitude);

    return 0;
}
