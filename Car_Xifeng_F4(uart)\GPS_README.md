# ATGM336H GPS模块使用指南

## 🔌 硬件连接
```
ATGM336H -> STM32F407
VCC      -> 3.3V
GND      -> GND
TX       -> PD9 (USART3_RX)
```

## 💻 代码使用
```c
int main(void) {
    // HAL初始化...

    Uart3_Init();  // 初始化GPS

    while (1) {
        Uart3_Task();  // 处理GPS
        HAL_Delay(100);
    }
}
```

## 📺 串口1输出示例
```
GPS ATGM336H Ready
UTC Time = 123519.00
Raw latitude = 4807.038
N_S = N
Raw longitude = 01131.000
E_W = E
Latitude: N, 48.117300
Longitude: E, 11.516667
Final: 48.117300, 11.516667
------------------------
```

## ⚠️ 注意事项
- **串口3**: 接收GPS的NMEA数据 (波特率9600)
- **串口1**: 显示解析后的GPS信息 (波特率115200)
- **室外测试**: 室内无法获得GPS信号
- **等待时间**: 冷启动需要1-3分钟
- **天线朝上**: 确保GPS天线朝向天空

## 🎯 功能特点
- ✅ 解析GPRMC/GNRMC语句
- ✅ 自动转换经纬度格式
- ✅ 处理东西经/南北纬
- ✅ 实时显示GPS数据
- ✅ 数据有效性检查

**编译测试即可使用！**
