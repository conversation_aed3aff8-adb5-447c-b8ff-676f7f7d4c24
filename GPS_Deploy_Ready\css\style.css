/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 2.2em;
    font-weight: 600;
}

.status-bar {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.label {
    font-weight: 600;
    color: #555;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
}

.status.online {
    background: #27ae60;
    color: white;
}

.status.offline {
    background: #e74c3c;
    color: white;
}

/* 地图容器样式 */
.map-container {
    position: relative;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

#map {
    height: 500px;
    width: 100%;
}

/* 控制面板样式 */
.control-panel {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-info {
    background: #9b59b6;
    color: white;
}

/* 信息面板样式 */
.info-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.info-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.info-label {
    font-weight: 600;
    color: #555;
}

/* 设置样式 */
.settings {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
}

.settings input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3498db;
}

.settings select {
    padding: 6px 10px;
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    background: white;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 1.8em;
    }
    
    .status-bar {
        flex-direction: column;
        align-items: center;
    }
    
    #map {
        height: 400px;
    }
    
    .control-panel {
        position: static;
        flex-direction: row;
        justify-content: center;
        padding: 15px;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* 自定义地图标记样式 */
.custom-marker {
    background: #e74c3c;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 导航标记样式 */
.navigation-marker {
    background: transparent;
    border: none;
}

.navigation-marker .marker-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.start-marker .marker-content {
    background: #27ae60;
    color: white;
}

.end-marker .marker-content {
    background: #e74c3c;
    color: white;
}

.waypoint-marker .marker-content {
    background: #f39c12;
    color: white;
    font-size: 12px;
}

/* 导航路线动画 */
@keyframes dash {
    to {
        stroke-dashoffset: -20;
    }
}
