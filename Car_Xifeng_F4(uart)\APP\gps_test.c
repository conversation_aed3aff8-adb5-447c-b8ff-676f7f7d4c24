/**
 * @file gps_test.c
 * @brief GPS模块测试程序
 * 
 * 这个文件提供了一个简单的GPS测试程序，可以用来验证ATGM336H GPS模块的功能
 */

#include "GPS_app.h"

/* 测试状态 */
static uint8_t test_running = 0;
static uint32_t test_start_time = 0;
static uint32_t last_test_print = 0;

/**
 * @brief 启动GPS测试
 */
void GPS_Test_Start(void)
{
    // my_printf(&huart1, "\r\n=== GPS Test Started ===\r\n");  // 已注释掉GPS测试开始信息
    // my_printf(&huart1, "Testing ATGM336H GPS module on UART3\r\n");  // 已注释掉GPS模块测试信息
    // my_printf(&huart1, "Please ensure GPS antenna is positioned outdoors or near window\r\n");  // 已注释掉GPS天线提示
    // my_printf(&huart1, "Waiting for GPS fix...\r\n");  // 已注释掉GPS定位等待信息

    test_running = 1;
    test_start_time = HAL_GetTick();
    last_test_print = HAL_GetTick();
}

/**
 * @brief 停止GPS测试
 */
void GPS_Test_Stop(void)
{
    if (test_running) {
        uint32_t test_duration = (HAL_GetTick() - test_start_time) / 1000;
        // my_printf(&huart1, "\r\n=== GPS Test Stopped ===\r\n");  // 已注释掉GPS测试停止信息
        // my_printf(&huart1, "Test duration: %lu seconds\r\n", test_duration);  // 已注释掉测试持续时间
        // my_printf(&huart1, "Total GPS updates: %lu\r\n", GPS_GetData()->update_count);  // 已注释掉GPS更新次数
        test_running = 0;
    }
}

/**
 * @brief GPS测试任务
 * 在主循环中调用
 */
void GPS_Test_Task(void)
{
    if (!test_running) {
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    uint32_t elapsed_time = (current_time - test_start_time) / 1000;
    
    // 每5秒打印一次测试状态
    if (current_time - last_test_print > 5000) {
        // my_printf(&huart1, "\r\n--- GPS Test Status (T+%lu s) ---\r\n", elapsed_time);  // 已注释掉GPS测试状态

        if (GPS_IsDataValid()) {
            GPS_Data_t* gps = GPS_GetData();
            // my_printf(&huart1, "✓ GPS FIX ACQUIRED!\r\n");  // 已注释掉GPS定位成功信息
            // my_printf(&huart1, "Position: %.6f°, %.6f°\r\n", gps->latitude, gps->longitude);  // 已注释掉位置信息
            // my_printf(&huart1, "Altitude: %.1f m\r\n", gps->altitude);  // 已注释掉海拔信息
            // my_printf(&huart1, "Satellites: %d\r\n", gps->satellites);  // 已注释掉卫星数量
            // my_printf(&huart1, "HDOP: %.2f\r\n", gps->hdop);  // 已注释掉HDOP信息
            // my_printf(&huart1, "Speed: %.1f knots\r\n", gps->speed);  // 已注释掉速度信息
            // my_printf(&huart1, "Time: %02d:%02d:%02d UTC\r\n", gps->hour, gps->minute, gps->second);  // 已注释掉时间信息
            // my_printf(&huart1, "Updates: %lu\r\n", gps->update_count);  // 已注释掉更新次数

            // 生成地图链接
            // my_printf(&huart1, "Map: https://maps.google.com/?q=%.6f,%.6f\r\n",
            //           gps->latitude, gps->longitude);  // 已注释掉地图链接
        } else {
            // my_printf(&huart1, "⏳ Searching for GPS satellites...\r\n");  // 已注释掉搜索卫星信息
            // my_printf(&huart1, "Elapsed time: %lu seconds\r\n", elapsed_time);  // 已注释掉经过时间

            if (elapsed_time > 60) {
                // my_printf(&huart1, "⚠️  No GPS fix after 1 minute\r\n");  // 已注释掉1分钟无定位警告
                // my_printf(&huart1, "Check antenna position and try outdoor location\r\n");  // 已注释掉天线位置建议
            }
        }

        // my_printf(&huart1, "--------------------------------\r\n");  // 已注释掉分隔线
        last_test_print = current_time;
    }
}

/**
 * @brief 处理GPS测试命令
 */
void GPS_Test_ProcessCommand(char* command)
{
    if (strcmp(command, "gps_start") == 0) {
        GPS_Test_Start();
    }
    else if (strcmp(command, "gps_stop") == 0) {
        GPS_Test_Stop();
    }
    else if (strcmp(command, "gps_data") == 0) {
        GPS_PrintData();
    }
    else if (strcmp(command, "gps_status") == 0) {
        GPS_PrintStatus();
    }
    else if (strcmp(command, "gps_raw") == 0) {
        // 显示原始NMEA数据 (需要在GPS_app.c中添加调试输出)
        my_printf(&huart1, "Raw NMEA data logging enabled\r\n");
    }
    else if (strcmp(command, "gps_help") == 0) {
        my_printf(&huart1, "\r\n=== GPS Test Commands ===\r\n");
        my_printf(&huart1, "gps_start  - Start GPS test\r\n");
        my_printf(&huart1, "gps_stop   - Stop GPS test\r\n");
        my_printf(&huart1, "gps_data   - Show GPS data\r\n");
        my_printf(&huart1, "gps_status - Show GPS status\r\n");
        my_printf(&huart1, "gps_raw    - Show raw NMEA data\r\n");
        my_printf(&huart1, "gps_help   - Show this help\r\n");
        my_printf(&huart1, "========================\r\n");
    }
}

/**
 * @brief 检查GPS测试是否正在运行
 */
uint8_t GPS_Test_IsRunning(void)
{
    return test_running;
}

/**
 * @brief 获取GPS测试统计信息
 */
void GPS_Test_GetStats(uint32_t* duration, uint32_t* updates, uint8_t* has_fix)
{
    if (duration) {
        *duration = test_running ? (HAL_GetTick() - test_start_time) / 1000 : 0;
    }
    
    if (updates) {
        *updates = GPS_GetData()->update_count;
    }
    
    if (has_fix) {
        *has_fix = GPS_IsDataValid();
    }
}

/**
 * @brief 模拟NMEA数据测试 (用于调试)
 */
void GPS_Test_SimulateNMEA(void)
{
    my_printf(&huart1, "Testing NMEA parsing with simulated data...\r\n");
    
    // 测试GPGGA语句
    char test_gpgga[] = "$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47";
    if (GPS_ParseNMEA(test_gpgga)) {
        my_printf(&huart1, "✓ GPGGA parsing successful\r\n");
        GPS_PrintData();
    } else {
        my_printf(&huart1, "✗ GPGGA parsing failed\r\n");
    }
    
    // 测试GPRMC语句
    char test_gprmc[] = "$GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A";
    if (GPS_ParseNMEA(test_gprmc)) {
        my_printf(&huart1, "✓ GPRMC parsing successful\r\n");
        GPS_PrintData();
    } else {
        my_printf(&huart1, "✗ GPRMC parsing failed\r\n");
    }
}

/* 
 * 使用示例：
 * 
 * 在主程序中：
 * 1. 调用 GPS_Init() 初始化GPS
 * 2. 在主循环中调用 GPS_Task() 和 GPS_Test_Task()
 * 3. 通过串口发送命令来控制测试
 * 
 * 串口命令：
 * - "gps_start" : 开始GPS测试
 * - "gps_stop"  : 停止GPS测试  
 * - "gps_data"  : 显示GPS数据
 * - "gps_status": 显示GPS状态
 * - "gps_help"  : 显示帮助信息
 */
