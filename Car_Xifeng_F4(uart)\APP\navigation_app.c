#include "navigation_app.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

Navigation_t current_navigation = {0};
NavigationState_t nav_state = NAV_STATE_IDLE;

static const Destination_t destinations[MAX_DESTINATIONS] = {
    {"wangda", 26.8869f, 112.6758f, "۹�����㳡"},
    {"gaotie", 26.8945f, 112.6123f, "����������վ"},
    {"daxue", 26.8869f, 112.6758f, "����ʦ��ѧԺ"},
    {"yiyuan", 26.8756f, 112.6234f, "�ϻ���ѧ������һҽԺ"},
    {"gongyuan", 26.8934f, 112.5967f, "ʯ�Ĺ�԰"},
    {"shangchang", 26.8823f, 112.6145f, "���¿�����"},
    {"jichang", 26.7345f, 112.6789f, "������������"},
    {"xuexiao", 26.8869f, 112.6758f, "����ʦ��ѧԺ��У��"},
    {"zhongxin", 26.8912f, 112.6034f, "����������"},
    {"huochezhan", 26.8834f, 112.6167f, "������վ"}
};

void Navigation_Init(void)
{
    nav_state = NAV_STATE_IDLE;
    memset(&current_navigation, 0, sizeof(Navigation_t));
    my_printf(&huart1, "����ϵͳ������������ nav_help �鿴����\r\n");
}

void Navigation_Task(void)
{
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - last_update < 1000) return;
    last_update = current_time;
    
    switch (nav_state) {
        case NAV_STATE_NAVIGATING:
            Navigation_UpdateProgress();
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "�ѵ���Ŀ�ĵ�: %s\r\n", current_navigation.destination.description);
            nav_state = NAV_STATE_IDLE;
            break;
        default:
            break;
    }
}

uint8_t Navigation_StartNavigation(const char* destination_name)
{
    Destination_t dest;
    if (!Navigation_FindDestination(destination_name, &dest)) {
        my_printf(&huart1, "δ�ҵ�Ŀ�ĵ�: %s\r\n", destination_name);
        return 0;
    }

    float current_lat = g_LatAndLongData.latitude;
    float current_lon = g_LatAndLongData.longitude;

    if (current_lat == 0.0f || current_lon == 0.0f) {
        my_printf(&huart1, "GPS�ź���Ч���޷���ʼ����\r\n");
        return 0;
    }
    
    current_navigation.destination = dest;
    current_navigation.current_waypoint = 0;
    current_navigation.is_active = 1;
    current_navigation.is_arrived = 0;
    
    Navigation_PlanRoute(current_lat, current_lon, dest.latitude, dest.longitude);
    
    nav_state = NAV_STATE_NAVIGATING;

    my_printf(&huart1, "��ʼ������: %s\r\n", dest.description);
    my_printf(&huart1, "�ܾ���: %.0f��\r\n", current_navigation.total_distance);

    // 上传GPS位置数据到ThingSpeak (使用持久连接)
    extern void esp01_SendLocationData(void);
    esp01_SendLocationData();

    return 1;
}

void Navigation_StopNavigation(void)
{
    nav_state = NAV_STATE_IDLE;
    current_navigation.is_active = 0;
    my_printf(&huart1, "������ֹͣ\r\n");
}

void Navigation_ProcessCommand(const char* command)
{
    if (strncmp(command, "nav_", 4) == 0) {
        const char* nav_cmd = command + 4;
        
        if (strcmp(nav_cmd, "help") == 0) {
            my_printf(&huart1, "����ϵͳ����:\r\n");
            my_printf(&huart1, "nav_help - ��ʾ����\r\n");
            my_printf(&huart1, "nav_list - ��ʾĿ�ĵ��б�\r\n");
            my_printf(&huart1, "nav_status - ��ʾ����״̬\r\n");
            my_printf(&huart1, "nav_stop - ֹͣ����\r\n");
            my_printf(&huart1, "wangda - ������۹�����\r\n");
            my_printf(&huart1, "gaotie - ����������վ\r\n");
        }
        else if (strcmp(nav_cmd, "list") == 0) {
            Navigation_PrintDestinations();
        }
        else if (strcmp(nav_cmd, "status") == 0) {
            Navigation_PrintStatus();
        }
        else if (strcmp(nav_cmd, "stop") == 0) {
            Navigation_StopNavigation();
        }
    }
    else {
        // ����Ƿ�ΪĿ�ĵ�����
        uint8_t found = 0;
        for (int i = 0; i < MAX_DESTINATIONS; i++) {
            if (strcmp(command, destinations[i].name) == 0) {
                Navigation_StartNavigation(command);
                found = 1;
                return;
            }
        }

        // ������ǵ������������ʾ
        if (!found && strlen(command) > 0) {
            my_printf(&huart1, "δ֪����: %s\r\n", command);
            my_printf(&huart1, "���� nav_help �鿴����\r\n");
        }
    }
}

uint8_t Navigation_FindDestination(const char* name, Destination_t* dest)
{
    for (int i = 0; i < MAX_DESTINATIONS; i++) {
        if (strcmp(name, destinations[i].name) == 0) {
            *dest = destinations[i];
            return 1;
        }
    }
    return 0;
}

void Navigation_PlanRoute(float start_lat, float start_lon, float end_lat, float end_lon)
{
    current_navigation.waypoint_count = 2;
    
    current_navigation.waypoints[0].latitude = start_lat;
    current_navigation.waypoints[0].longitude = start_lon;
    strcpy(current_navigation.waypoints[0].instruction, "�ӵ�ǰλ�ó���");

    current_navigation.waypoints[1].latitude = end_lat;
    current_navigation.waypoints[1].longitude = end_lon;
    strcpy(current_navigation.waypoints[1].instruction, "����Ŀ�ĵ�");
    
    float distance = Navigation_CalculateDistance(start_lat, start_lon, end_lat, end_lon);
    float bearing = Navigation_CalculateBearing(start_lat, start_lon, end_lat, end_lon);
    
    current_navigation.waypoints[0].distance_to_next = distance;
    current_navigation.waypoints[0].bearing_to_next = bearing;
    current_navigation.total_distance = distance;
    current_navigation.remaining_distance = distance;
    
    const char* direction = "";
    if (bearing >= 337.5f || bearing < 22.5f) direction = "��";
    else if (bearing >= 22.5f && bearing < 67.5f) direction = "����";
    else if (bearing >= 67.5f && bearing < 112.5f) direction = "��";
    else if (bearing >= 112.5f && bearing < 157.5f) direction = "����";
    else if (bearing >= 157.5f && bearing < 202.5f) direction = "��";
    else if (bearing >= 202.5f && bearing < 247.5f) direction = "����";
    else if (bearing >= 247.5f && bearing < 292.5f) direction = "��";
    else direction = "����";
    
    snprintf(current_navigation.waypoints[0].instruction, 128,
             "��%s������ʻ %.0f��", direction, distance);
}

float Navigation_CalculateDistance(float lat1, float lon1, float lat2, float lon2)
{
    float dlat = (lat2 - lat1) * M_PI / 180.0f;
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float a = sinf(dlat/2) * sinf(dlat/2) + cosf(lat1 * M_PI / 180.0f) * 
              cosf(lat2 * M_PI / 180.0f) * sinf(dlon/2) * sinf(dlon/2);
    float c = 2 * atan2f(sqrtf(a), sqrtf(1-a));
    return EARTH_RADIUS * c;
}

float Navigation_CalculateBearing(float lat1, float lon1, float lat2, float lon2)
{
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float lat1_rad = lat1 * M_PI / 180.0f;
    float lat2_rad = lat2 * M_PI / 180.0f;
    
    float y = sinf(dlon) * cosf(lat2_rad);
    float x = cosf(lat1_rad) * sinf(lat2_rad) - sinf(lat1_rad) * cosf(lat2_rad) * cosf(dlon);
    
    float bearing = atan2f(y, x) * 180.0f / M_PI;
    return fmodf(bearing + 360.0f, 360.0f);
}

void Navigation_UpdateProgress(void)
{
    float current_lat = g_LatAndLongData.latitude;
    float current_lon = g_LatAndLongData.longitude;
    
    if (current_lat == 0.0f || current_lon == 0.0f) return;
    
    float distance_to_dest = Navigation_CalculateDistance(
        current_lat, current_lon,
        current_navigation.destination.latitude,
        current_navigation.destination.longitude
    );
    
    current_navigation.remaining_distance = distance_to_dest;
    
    if (distance_to_dest < 50.0f) {
        nav_state = NAV_STATE_ARRIVED;
        current_navigation.is_arrived = 1;
        return;
    }
    
    static uint32_t last_instruction = 0;
    if (HAL_GetTick() - last_instruction > 10000) {
        float bearing = Navigation_CalculateBearing(
            current_lat, current_lon,
            current_navigation.destination.latitude,
            current_navigation.destination.longitude
        );
        
        const char* direction = "";
        if (bearing >= 337.5f || bearing < 22.5f) direction = "��";
        else if (bearing >= 22.5f && bearing < 67.5f) direction = "����";
        else if (bearing >= 67.5f && bearing < 112.5f) direction = "��";
        else if (bearing >= 112.5f && bearing < 157.5f) direction = "����";
        else if (bearing >= 157.5f && bearing < 202.5f) direction = "��";
        else if (bearing >= 202.5f && bearing < 247.5f) direction = "����";
        else if (bearing >= 247.5f && bearing < 292.5f) direction = "��";
        else direction = "����";
        
        my_printf(&huart1, "������%s������ʻ������%.0f�׵���%s\r\n",
                  direction, distance_to_dest, current_navigation.destination.description);
        
        last_instruction = HAL_GetTick();
    }
}

void Navigation_PrintStatus(void)
{
    my_printf(&huart1, "����״̬:\r\n");

    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart1, "״̬: ����\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart1, "״̬: ������\r\n");
            my_printf(&huart1, "Ŀ�ĵ�: %s\r\n", current_navigation.destination.description);
            my_printf(&huart1, "ʣ�����: %.0f��\r\n", current_navigation.remaining_distance);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "״̬: �ѵ���\r\n");
            break;
        default:
            my_printf(&huart1, "״̬: δ֪\r\n");
            break;
    }
}

void Navigation_PrintDestinations(void)
{
    my_printf(&huart1, "����Ŀ�ĵ�:\r\n");
    for (int i = 0; i < MAX_DESTINATIONS && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart1, "%s - %s\r\n", destinations[i].name, destinations[i].description);
    }
}