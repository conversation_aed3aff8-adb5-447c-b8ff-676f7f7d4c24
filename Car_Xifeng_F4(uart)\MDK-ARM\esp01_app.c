/**
 * ESP01 WiFi Module Application - Real-time GPS Tracking System
 *
 * Main Functions:
 * 1. WiFi Connection Management - Connect to specified WiFi network
 * 2. Real-time GPS Upload - Upload GPS coordinates to ThingSpeak cloud
 * 3. Map Display - Show current position on web map
 * 4. Automatic GPS Data Upload - Every 15 seconds
 *
 * Author: AI Assistant
 * Version: 3.0 - Simplified GPS Tracking
 * Date: 2025-01-27
 */

#include "esp01_app.h"
#include "GPS_app.h"
#include "stdlib.h"

extern uint8_t uart2_rx_dma_buffer[];
extern DMA_HandleTypeDef hdma_usart2_rx;

#define UART2_BUFFER_SIZE 512

// ==================== System State Management ====================
static ESP01_State_t esp01_state = ESP01_STATE_IDLE;
static uint32_t esp01_last_cmd_time = 0;
static uint8_t esp01_retry_count = 0;
static uint8_t esp01_init_step = 0;

// ==================== Network Connection Status ====================
static volatile uint8_t tcp_connected = 0;
static volatile uint8_t data_send_ready = 0;
static volatile uint8_t persistent_connection = 0;

// ==================== WiFi Configuration ====================
#define WIFI_SSID "Tenda_ZC_5G"
#define WIFI_PASSWORD "zhongchuang"
#define ESP01_MAX_RETRY 3
#define ESP01_CMD_TIMEOUT 5000

// ==================== ThingSpeak Cloud Service Configuration ====================
#define THINGSPEAK_API_KEY "LU22ZUP4ZTFK4IY9"
#define THINGSPEAK_HOST "api.thingspeak.com"
#define THINGSPEAK_IP "*************"  // ThingSpeak IP address backup (updated 2024)
#define THINGSPEAK_CHANNEL "3014831"

// ==================== GPS Upload Interval ====================
#define GPS_UPLOAD_INTERVAL 10000  // Upload every 10 seconds (faster for testing)

// ==================== Data Buffers ====================
static char http_request_buffer[400];
static char url_params_buffer[300];

/**
 * ESP01 Initialization Function
 * Set initial state and start WiFi connection process
 */
void esp01_Init()
{
    esp01_state = ESP01_STATE_INIT;
    esp01_last_cmd_time = HAL_GetTick();
    esp01_retry_count = 0;
    esp01_init_step = 0;
    tcp_connected = 0;
    data_send_ready = 0;
    persistent_connection = 0;

    my_printf(&huart1, "\r\n========== ESP01 GPS Tracking System ==========\r\n");
    my_printf(&huart1, "WiFi Network: %s\r\n", WIFI_SSID);
    my_printf(&huart1, "ThingSpeak Channel: %s\r\n", THINGSPEAK_CHANNEL);
    my_printf(&huart1, "Upload Interval: %d seconds\r\n", GPS_UPLOAD_INTERVAL/1000);
    my_printf(&huart1, "Map URL: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/\r\n");
    my_printf(&huart1, "===============================================\r\n\r\n");
}

/**
 * ESP01 Main Task Function
 * State machine for WiFi connection and data upload management
 */
void esp01_Task()
{
    uint32_t current_time = HAL_GetTick();

    switch(esp01_state)
    {
        case ESP01_STATE_IDLE:
            // Idle state, waiting for startup
            break;

        case ESP01_STATE_INIT:
            // Initialization state, execute WiFi connection sequence
            esp01_InitSequence();
            break;

        case ESP01_STATE_CONNECTING:
            // Connecting state, handle timeout and retry
            if(current_time - esp01_last_cmd_time > ESP01_CMD_TIMEOUT)
            {
                esp01_retry_count++;
                if(esp01_retry_count >= ESP01_MAX_RETRY)
                {
                    my_printf(&huart1, "ESP01: WiFi connection failed, resetting...\r\n");
                    esp01_state = ESP01_STATE_INIT;
                    esp01_init_step = 0;
                    esp01_retry_count = 0;
                }
                else
                {
                    my_printf(&huart1, "ESP01: Retry connection %d/%d\r\n", esp01_retry_count, ESP01_MAX_RETRY);
                    esp01_state = ESP01_STATE_INIT;
                    esp01_init_step = 0;
                }
                esp01_last_cmd_time = current_time;
            }
            break;

        case ESP01_STATE_CONNECTED:
            // Connected state, periodically upload GPS data
            {
                static uint32_t last_upload_time = 0;
                if(current_time - last_upload_time > GPS_UPLOAD_INTERVAL)
                {
                    my_printf(&huart1, "Starting GPS data upload...\r\n");
                    esp01_UploadGPSData();
                    last_upload_time = current_time;
                }

                // Periodically check connection status
                if(current_time - esp01_last_cmd_time > 300000) // Check every 5 minutes
                {
                    esp01_CheckConnection();
                    esp01_last_cmd_time = current_time;
                }
            }
            break;

        case ESP01_STATE_ERROR:
            // Error state, waiting for reset
            break;

        default:
            esp01_state = ESP01_STATE_IDLE;
            break;
    }
}

/**
 * WiFi Initialization Sequence
 * Execute AT commands step by step to connect WiFi
 */
void esp01_InitSequence()
{
    uint32_t current_time = HAL_GetTick();
    uint32_t wait_time = (esp01_init_step == 2) ? 5000 : 2000;

    if(current_time - esp01_last_cmd_time < wait_time)
    {
        return; // Wait for command execution to complete
    }

    switch(esp01_init_step)
    {
        case 0:
            my_printf(&huart1, "Step 1: Testing ESP01 communication...\r\n");
            Uart2_Printf(&huart2, "AT\r\n");
            esp01_init_step++;
            break;

        case 1:
            my_printf(&huart1, "Step 2: Restarting ESP01 module...\r\n");
            Uart2_Printf(&huart2, "AT+RST\r\n");
            esp01_init_step++;
            esp01_last_cmd_time = current_time;
            break;

        case 2:
            my_printf(&huart1, "Step 3: Setting WiFi mode...\r\n");
            HAL_UART_DMAStop(&huart2);
            HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, UART2_BUFFER_SIZE);
            __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
            Uart2_Printf(&huart2, "AT+CWMODE=1\r\n");
            esp01_init_step++;
            break;

        case 3:
            my_printf(&huart1, "Step 4: Connecting to WiFi [%s]...\r\n", WIFI_SSID);
            Uart2_Printf(&huart2, "AT+CWJAP=\"%s\",\"%s\"\r\n", WIFI_SSID, WIFI_PASSWORD);
            esp01_state = ESP01_STATE_CONNECTING;
            esp01_init_step = 0;
            break;

        default:
            esp01_init_step = 0;
            break;
    }
    esp01_last_cmd_time = current_time;
}

/**
 * Check WiFi Connection Status
 */
void esp01_CheckConnection()
{
    Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
    my_printf(&huart1, "ESP01: Checking WiFi connection status\r\n");
}

/**
 * Get ESP01 Current State
 */
ESP01_State_t esp01_GetState()
{
    return esp01_state;
}

/**
 * Set WiFi Connection Success Status
 */
void esp01_SetConnected()
{
    if(esp01_state != ESP01_STATE_CONNECTED)
    {
        esp01_state = ESP01_STATE_CONNECTED;
        esp01_retry_count = 0;
        my_printf(&huart1, "ESP01: WiFi connected successfully!\r\n");
        my_printf(&huart1, "Network: %s\r\n", WIFI_SSID);
        my_printf(&huart1, "Ready for data transmission...\r\n");

        // After WiFi connection success, establish TCP connection
        HAL_Delay(2000);
        esp01_EstablishTCPConnection();
    }
}

/**
 * Set TCP Connection Success Status
 */
void esp01_SetTCPConnected()
{
    tcp_connected = 1;
    persistent_connection = 1;
    my_printf(&huart1, "TCP connection established successfully\r\n");
}

/**
 * Set Data Send Ready Status
 */
void esp01_SetDataSendReady()
{
    data_send_ready = 1;
    my_printf(&huart1, "ESP01: Ready to send data\r\n");
}

/**
 * Reset TCP Connection Status
 */
void esp01_ResetTCPState()
{
    tcp_connected = 0;
    data_send_ready = 0;
    persistent_connection = 0;
}

/**
 * Reset ESP01 Module
 */
void esp01_Reset()
{
    esp01_state = ESP01_STATE_INIT;
    esp01_last_cmd_time = HAL_GetTick();
    esp01_retry_count = 0;
    esp01_init_step = 0;
    esp01_ResetTCPState();
    my_printf(&huart1, "ESP01: Module reset\r\n");
}

/**
 * Send Custom AT Command
 */
void esp01_SendCommand(const char* command)
{
    Uart2_Printf(&huart2, "%s\r\n", command);
    my_printf(&huart1, "Send AT command: %s\r\n", command);
}

/**
 * Check TCP Connection Status
 * 检查TCP连接状态
 */
uint8_t esp01_CheckTCPStatus()
{
    my_printf(&huart1, "🔍 Checking TCP connection status...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
    HAL_Delay(1000);
    return tcp_connected;
}

/**
 * Try TCP Connection with IP Address
 * 使用IP地址尝试TCP连接（绕过DNS）
 */
uint8_t esp01_TryTCPWithIP()
{
    my_printf(&huart1, "🔄 Trying TCP connection with IP address %s...\r\n", THINGSPEAK_IP);

    // 重置TCP状态
    esp01_ResetTCPState();

    // 使用IP地址建立连接
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",80\r\n", THINGSPEAK_IP);

    // 等待连接建立
    uint32_t start_time = HAL_GetTick();
    uint32_t timeout = 15000; // 15秒超时

    while (!tcp_connected && (HAL_GetTick() - start_time) < timeout) {
        HAL_Delay(500);

        if (((HAL_GetTick() - start_time) % 3000) < 500) {
            uint32_t elapsed = (HAL_GetTick() - start_time) / 1000;
            my_printf(&huart1, "⏳ IP connection attempt... %ds/%ds\r\n", elapsed, timeout/1000);
        }
    }

    if (tcp_connected) {
        my_printf(&huart1, "✅ TCP connection via IP successful!\r\n");
        return 1;
    } else {
        my_printf(&huart1, "❌ TCP connection via IP failed\r\n");
        return 0;
    }
}

/**
 * Establish TCP Connection to ThingSpeak Server
 */
uint8_t esp01_EstablishTCPConnection()
{
    // 如果已经有持久连接且TCP已连接，先验证状态
    if(persistent_connection && tcp_connected) {
        my_printf(&huart1, "🔄 Verifying existing TCP connection...\r\n");
        if (esp01_CheckTCPStatus()) {
            my_printf(&huart1, "✅ TCP connection verified and active, reusing...\r\n");
            return 1;
        } else {
            my_printf(&huart1, "⚠️ Existing connection invalid, establishing new one...\r\n");
            esp01_ResetTCPState();
        }
    }

    if(esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "❌ WiFi not connected, cannot establish TCP connection\r\n");
        return 0;
    }

    my_printf(&huart1, "🌐 Establishing TCP connection to %s:80...\r\n", THINGSPEAK_HOST);

    // 重置TCP状态
    esp01_ResetTCPState();

    // 强制关闭所有连接
    my_printf(&huart1, "🔌 Closing all existing connections...\r\n");
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(2000);

    // 设置单连接模式
    my_printf(&huart1, "⚙️ Setting single connection mode...\r\n");
    Uart2_Printf(&huart2, "AT+CIPMUX=0\r\n");
    HAL_Delay(1000);

    // 方法1：尝试使用域名连接
    my_printf(&huart1, "🔍 Method 1: Trying connection with domain name...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",80\r\n", THINGSPEAK_HOST);

    // 等待连接建立 - 较短超时
    uint32_t start_time = HAL_GetTick();
    uint32_t timeout = 10000; // 10秒超时

    while (!tcp_connected && (HAL_GetTick() - start_time) < timeout) {
        HAL_Delay(500);

        if (((HAL_GetTick() - start_time) % 3000) < 500) {
            uint32_t elapsed = (HAL_GetTick() - start_time) / 1000;
            my_printf(&huart1, "⏳ Domain connection... %ds/%ds\r\n", elapsed, timeout/1000);
        }
    }

    if (tcp_connected) {
        persistent_connection = 1;
        my_printf(&huart1, "✅ TCP connection via domain successful!\r\n");
        return 1;
    }

    // 方法2：如果域名失败，尝试IP地址
    my_printf(&huart1, "⚠️ Domain connection failed, trying IP address...\r\n");

    // 关闭失败的连接尝试
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    if (esp01_TryTCPWithIP()) {
        persistent_connection = 1;
        my_printf(&huart1, "✅ TCP connection established via IP backup!\r\n");
        return 1;
    }

    // 两种方法都失败
    my_printf(&huart1, "❌ All TCP connection methods failed\r\n");
    my_printf(&huart1, "🔧 Please check: 1) WiFi signal 2) Internet access 3) Firewall settings\r\n");
    esp01_ResetTCPState();
    return 0;
}

/**
 * Get Current GPS Location
 */
void esp01_GetRealLocation(float* lat, float* lon, float* alt)
{
    *lat = g_LatAndLongData.latitude;
    *lon = g_LatAndLongData.longitude;
    *alt = 50.0f;

    // If GPS data is invalid, use Hengyang Normal University as default location
    if (*lat == 0.0f || *lon == 0.0f) {
        *lat = 26.88693f;
        *lon = 112.675813f;
        *alt = 50.0f;
        my_printf(&huart1, "GPS data invalid, using default location\r\n");
    } else {
        my_printf(&huart1, "Using real GPS data: %.6fN, %.6fE\r\n", *lat, *lon);
    }
}

/**
 * Upload GPS Data to ThingSpeak
 */
void esp01_UploadGPSData()
{
    if(esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "ESP01 not connected, cannot upload GPS data\r\n");
        return;
    }

    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    // Build URL parameters
    snprintf(url_params_buffer, sizeof(url_params_buffer),
             "/update?api_key=%s&field1=%.6f&field2=%.6f&field3=%.1f",
             THINGSPEAK_API_KEY, lat, lon, alt);

    // Build HTTP request (use keep-alive to maintain connection)
    snprintf(http_request_buffer, sizeof(http_request_buffer),
             "GET %s HTTP/1.1\r\n"
             "Host: %s\r\n"
             "Connection: keep-alive\r\n\r\n",
             url_params_buffer, THINGSPEAK_HOST);

    my_printf(&huart1, "📍 Location: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
    my_printf(&huart1, "🌐 URL: %s%s\r\n", THINGSPEAK_HOST, url_params_buffer);

    // 使用增强的数据发送机制
    int request_length = strlen(http_request_buffer);
    if (esp01_SendDataWithRecovery(http_request_buffer, request_length)) {
        my_printf(&huart1, "✅ GPS data upload successful!\r\n");
    } else {
        my_printf(&huart1, "❌ GPS data upload failed after retries\r\n");
        return;
    }

    // 显示上传结果和位置信息
    my_printf(&huart1, "\r\n========== GPS Upload Complete ==========\r\n");
    if (lat == 26.88693f && lon == 112.675813f) {
        my_printf(&huart1, "📍 Location: [DEFAULT] Hengyang Normal University\r\n");
        my_printf(&huart1, "🔧 Note: Using default coordinates (GPS not available)\r\n");
    } else {
        my_printf(&huart1, "📍 Location: [REAL GPS] Current Position\r\n");
        my_printf(&huart1, "✅ GPS data is valid and current\r\n");
    }
    my_printf(&huart1, "🌐 Coordinates: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
    my_printf(&huart1, "📡 ThingSpeak Channel: %s\r\n", THINGSPEAK_CHANNEL);
    my_printf(&huart1, "🗺️ View Live Map: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/\r\n");
    my_printf(&huart1, "⏰ Next upload in %d seconds\r\n", GPS_UPLOAD_INTERVAL/1000);
    my_printf(&huart1, "=========================================\r\n\r\n");
}

/**
 * Send Location Data (Compatibility Function)
 * This function calls esp01_UploadGPSData for backward compatibility
 */
void esp01_SendLocationData()
{
    esp01_UploadGPSData();
}



/**
 * Check and Recover TCP Connection
 * 检查TCP连接状态，如果断开则尝试重新连接
 */
uint8_t esp01_CheckAndRecoverTCPConnection()
{
    // 首先检查WiFi连接状态
    if (esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "❌ WiFi not connected, cannot recover TCP\r\n");
        return 0;
    }

    // 如果TCP连接标志显示未连接，尝试恢复
    if (!tcp_connected || !persistent_connection) {
        my_printf(&huart1, "🔄 TCP connection lost, attempting recovery...\r\n");

        // 重置状态并尝试重新连接
        esp01_ResetTCPState();

        // 多次尝试建立连接
        for (int attempt = 1; attempt <= 3; attempt++) {
            my_printf(&huart1, "🔄 Recovery attempt %d/3...\r\n", attempt);

            if (esp01_EstablishTCPConnection()) {
                my_printf(&huart1, "✅ TCP connection recovered on attempt %d\r\n", attempt);
                return 1;
            }

            if (attempt < 3) {
                my_printf(&huart1, "⏳ Waiting 3s before next attempt...\r\n");
                HAL_Delay(3000);
            }
        }

        my_printf(&huart1, "❌ TCP connection recovery failed after 3 attempts\r\n");
        return 0;
    }

    // 连接状态正常，但验证一下
    my_printf(&huart1, "🔍 TCP connection appears active, verifying...\r\n");
    return esp01_CheckTCPStatus();
}

/**
 * Enhanced Data Send with Connection Recovery
 * 增强的数据发送函数，包含连接恢复机制
 */
uint8_t esp01_SendDataWithRecovery(const char* data, uint16_t length)
{
    uint8_t retry_count = 0;
    const uint8_t max_retries = 2;

    while (retry_count < max_retries) {
        // 检查并恢复TCP连接
        if (!esp01_CheckAndRecoverTCPConnection()) {
            retry_count++;
            my_printf(&huart1, "🔄 Retry %d/%d: Connection recovery failed\r\n", retry_count, max_retries);
            HAL_Delay(2000);
            continue;
        }

        // 重置数据发送状态
        data_send_ready = 0;

        my_printf(&huart1, "📡 Sending CIPSEND command for %d bytes (attempt %d)...\r\n", length, retry_count + 1);
        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", length);

        // 等待">"提示符
        uint32_t timeout = HAL_GetTick() + 5000;
        while (!data_send_ready && HAL_GetTick() < timeout) {
            HAL_Delay(50);
        }

        if (data_send_ready) {
            my_printf(&huart1, "📤 Transmitting data...\r\n");
            Uart2_Printf(&huart2, "%s", data);
            HAL_Delay(1000);
            my_printf(&huart1, "✅ Data sent successfully\r\n");
            return 1;
        } else {
            my_printf(&huart1, "❌ Timeout waiting for '>' prompt\r\n");
            esp01_ResetTCPState();
            retry_count++;
            HAL_Delay(1000);
        }
    }

    my_printf(&huart1, "❌ Failed to send data after %d attempts\r\n", max_retries);
    return 0;
}

/**
 * Network Diagnostics Function
 * 网络诊断函数，用于排查连接问题
 */
void esp01_NetworkDiagnostics()
{
    my_printf(&huart1, "\r\n========== ESP01 Network Diagnostics ==========\r\n");

    // 检查WiFi连接状态
    my_printf(&huart1, "1. Checking WiFi connection...\r\n");
    Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
    HAL_Delay(2000);

    // 检查IP地址
    my_printf(&huart1, "2. Checking IP address...\r\n");
    Uart2_Printf(&huart2, "AT+CIFSR\r\n");
    HAL_Delay(2000);

    // 检查连接状态
    my_printf(&huart1, "3. Checking connection status...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
    HAL_Delay(2000);

    // 尝试ping测试（如果支持）
    my_printf(&huart1, "4. Testing DNS resolution...\r\n");
    Uart2_Printf(&huart2, "AT+CIPDOMAIN=\"%s\"\r\n", THINGSPEAK_HOST);
    HAL_Delay(3000);

    my_printf(&huart1, "========== Diagnostics Complete ==========\r\n\r\n");
}

/**
 * Force Reset ESP01 Module
 * 强制重置ESP01模块
 */
void esp01_ForceReset()
{
    my_printf(&huart1, "🔄 Force resetting ESP01 module...\r\n");

    // 关闭所有连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    // 重置模块
    Uart2_Printf(&huart2, "AT+RST\r\n");
    HAL_Delay(3000);

    // 重置所有状态
    esp01_state = ESP01_STATE_IDLE;
    esp01_last_cmd_time = HAL_GetTick();
    esp01_retry_count = 0;
    esp01_init_step = 0;
    esp01_ResetTCPState();

    my_printf(&huart1, "✅ ESP01 module reset complete\r\n");
    my_printf(&huart1, "Please wait for module to restart and run 'esp_start'\r\n");
}

/**
 * Start Initialization Sequence
 */
void esp01_StartInit()
{
    if(esp01_state == ESP01_STATE_IDLE)
    {
        esp01_state = ESP01_STATE_INIT;
        esp01_last_cmd_time = HAL_GetTick();
        esp01_retry_count = 0;
        esp01_init_step = 0;
        my_printf(&huart1, "Starting ESP01 initialization sequence\r\n");
    }
}